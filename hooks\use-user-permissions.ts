import { useState, useEffect, useCallback, useMemo } from 'react'
import { useMe } from './use-me'
import { Permission } from '@/types/user'

interface UseUserPermissionsReturn {
  permissions: Permission[]
  loading: boolean
  error: string | null
  hasPermission: (codename: string) => boolean
  refetch: () => void
}

/**
 * Hook to manage user permissions based on the /user/me endpoint
 * Extracts permissions from user groups and provides utility functions
 */
export function useUserPermissions(): UseUserPermissionsReturn {
  const { user, loading: userLoading, error: userError, refetch } = useMe()
  
  // Extract all permissions from user groups and direct permissions
  const permissions = useMemo(() => {
    if (!user?.user) return []
    
    const groupPermissions = user.user.groups?.flatMap(group => group.permissions) || []
    const directPermissions = user.user.user_permissions || []
    
    // Combine and deduplicate permissions by ID
    const allPermissions = [...groupPermissions, ...directPermissions]
    const uniquePermissions = allPermissions.filter((permission, index, self) => 
      index === self.findIndex(p => p.id === permission.id)
    )
    
    return uniquePermissions
  }, [user])
  
  // Function to check if user has a specific permission by codename
  const hasPermission = useCallback((codename: string): boolean => {
    return permissions.some(permission => permission.codename === codename)
  }, [permissions])

  useEffect(() => {
    refetch();
  }, [])
  
  return {
    permissions,
    loading: userLoading,
    error: userError,
    hasPermission,
    refetch
  }
}

// Pre-defined permission codenames for easy reference
export const PERMISSION_CODENAMES = {
  CREATE_EDIT_NEWSLETTERS: 'crear_i_editar_newsletters',
  VIEW_NEWSLETTERS: 'veure_newsletters', 
  VIEW_MANAGE_BLOCKS: 'veure_i_gestionar_blocs',
  VIEW_MANAGE_HEADERS_FOOTERS: 'veure_i_gestionar_capcaleres_i_peus',
  VIEW_MANAGE_TEMPLATES: 'veure_i_gestionar_plantilles',
  VIEW_MANAGE_USERS_ROLES: 'veure_i_gestionar_usuaris_i_rols'
} as const

export type PermissionCodename = typeof PERMISSION_CODENAMES[keyof typeof PERMISSION_CODENAMES]