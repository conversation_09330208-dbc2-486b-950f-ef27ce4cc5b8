"use client"

import { usePathname } from "next/navigation"
import Link from "next/link"
import { useEffect } from "react"
import { Spinner } from "@/components/ui/shadcn-io/spinner"

export default function Page() {
    const pathname = usePathname()
    useEffect(() => {
        window.location.href = "/"
    }, [])
    return (
        <div className="flex items-center justify-center min-h-screen">
            <Spinner variant="infinite" size={64} />
        </div>
    )
}