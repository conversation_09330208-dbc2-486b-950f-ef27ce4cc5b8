"use client"

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { useLanguages } from '@/hooks/use-languages'
import { NewsletterLanguage } from '@/types/newsletter'

interface LanguageContextType {
  selectedLanguage: string
  setSelectedLanguage: (language: string) => void
  languages: Array<{ language: string; language_display: string }>
  loading: boolean
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined)

interface LanguageProviderProps {
  children: ReactNode
  defaultLanguage?: string
  newsletterLanguages?: NewsletterLanguage[] // Optional newsletter languages to override API languages
}

export function LanguageProvider({
  children,
  defaultLanguage = "ca",
  newsletterLanguages
}: LanguageProviderProps) {
  const { languages: apiLanguages, loading: apiLoading } = useLanguages()
  const [selectedLanguage, setSelectedLanguage] = useState<string>(defaultLanguage)

  // Use newsletter languages if provided, otherwise use API languages
  const languages = newsletterLanguages
    ? newsletterLanguages.map(lang => ({
        language: lang.language,
        language_display: lang.language_display
      }))
    : apiLanguages

  // Loading state: if newsletter languages are provided, we're not loading
  // Otherwise, use the API loading state
  const loading = newsletterLanguages ? false : apiLoading

  // Set default language when languages load
  useEffect(() => {
    if (languages.length > 0 && !selectedLanguage) {
      setSelectedLanguage(languages[0].language)
    }
  }, [languages, selectedLanguage])

  // Update selected language if it's not available in the current language set
  useEffect(() => {
    if (languages.length > 0 && selectedLanguage) {
      const isLanguageAvailable = languages.some(lang => lang.language === selectedLanguage)
      if (!isLanguageAvailable) {
        setSelectedLanguage(languages[0].language)
      }
    }
  }, [languages, selectedLanguage])

  const value: LanguageContextType = {
    selectedLanguage,
    setSelectedLanguage,
    languages,
    loading
  }

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  )
}

export function useLanguageContext() {
  const context = useContext(LanguageContext)
  if (context === undefined) {
    throw new Error('useLanguageContext must be used within a LanguageProvider')
  }
  return context
}
