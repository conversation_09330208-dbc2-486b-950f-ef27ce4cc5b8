import { useState, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { NewsletterBuilderData } from '@/types/newsletter'
import { DEFAULT_API_URL } from '@/constants/constants'
import { extractApiErrorMessage } from '@/lib/api-error-utils'

interface TranslateRequest {
  newsletter_id: string
  source_language: string
  user_input: string
}

interface TranslateResponse {
  newsletter: NewsletterBuilderData
  sourceLanguage: string
  userInput: string
}

interface UseTranslateApiReturn {
  translateNewsletter: (request: TranslateRequest) => Promise<TranslateResponse>
  loading: boolean
  error: string | null
}

const API_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_URL || DEFAULT_API_URL

export function useTranslateApi(): UseTranslateApiReturn {
  const { data: session } = useSession()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const getAuthHeaders = useCallback(() => {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    }

    if (session?.djangoAccessToken) {
      headers['Authorization'] = `Bearer ${session.djangoAccessToken}`
    }

    return headers
  }, [session?.djangoAccessToken])

  const translateNewsletter = useCallback(async (request: TranslateRequest): Promise<TranslateResponse> => {
    if (!session?.djangoAccessToken) {
      throw new Error('No authentication token available')
    }

    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`${API_BASE_URL}/openai/traduce-newsletter/`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify(request)
      })

      if (!response.ok) {
        const errorMessage = await extractApiErrorMessage(response, 'Failed to translate newsletter')
        throw new Error(errorMessage)
      }

      const newsletterData: NewsletterBuilderData = await response.json()

      const translationResponse: TranslateResponse = {
        newsletter: newsletterData,
        sourceLanguage: request.source_language,
        userInput: request.user_input
      }

      return translationResponse
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while translating newsletter'
      setError(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }, [session?.djangoAccessToken, getAuthHeaders])

  return {
    translateNewsletter,
    loading,
    error
  }
}


