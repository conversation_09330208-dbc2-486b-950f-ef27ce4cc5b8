import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  const debugInfo = {
    environment: {
      NODE_ENV: process.env.NODE_ENV,
      NEXTAUTH_URL: process.env.NEXTAUTH_URL,
      AZURE_AD_CLIENT_ID: process.env.AZURE_AD_CLIENT_ID ? '✅ Set' : '❌ Missing',
      AZURE_AD_CLIENT_SECRET: process.env.AZURE_AD_CLIENT_SECRET ? '✅ Set' : '❌ Missing',
      AZURE_AD_TENANT_ID: process.env.AZURE_AD_TENANT_ID ? '✅ Set' : '❌ Missing',
      BACKEND_URL: process.env.BACKEND_URL,
    },
    timestamp: new Date().toISOString(),
    url: request.url,
  }

  return NextResponse.json(debugInfo)
}
