import { useState, useEffect, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { DEFAULT_API_URL } from '@/constants/constants'
import { extractApiErrorMessage } from '@/lib/api-error-utils'

interface NewsletterStatus {
  status: string
  status_display: string
}

interface NewsletterStatusHook {
  statuses: NewsletterStatus[]
  loading: boolean
  error: string | null
  refetch: () => void
}

export function useNewsletterStatus(): NewsletterStatusHook {
  const { data: session } = useSession()
  const [statuses, setStatuses] = useState<NewsletterStatus[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchStatuses = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      if (!session?.djangoAccessToken) {
        throw new Error('Usuari no autenticat')
      }

      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || DEFAULT_API_URL

      const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.djangoAccessToken}`
      }

      const response = await fetch(`${backendUrl}/newsletters/list-states/`, {
        method: 'GET',
        headers,
      })

      if (!response.ok) {
        const errorMessage = await extractApiErrorMessage(response, 'No s\'ha pogut carregar els estats dels butlletins')
        throw new Error(errorMessage)
      }

      const data: NewsletterStatus[] = await response.json()
      setStatuses(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'S\'ha produït un error')
      setStatuses([])
    } finally {
      setLoading(false)
    }
  }, [session?.djangoAccessToken])

  useEffect(() => {
    fetchStatuses()
  }, [fetchStatuses])

  return { statuses, loading, error, refetch: fetchStatuses }
}

