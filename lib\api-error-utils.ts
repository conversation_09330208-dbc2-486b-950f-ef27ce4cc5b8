/**
 * Utility function to extract detailed error messages from API responses
 * Looks for 'detail' field first, then 'message' field, then falls back to generic message
 */
export async function extractApiErrorMessage(
  response: Response,
  fallbackMessage: string
): Promise<string> {
  try {
    const errorData = await response.json()
    
    // First, check for 'detail' field (primary backend error format)
    if (errorData.detail && typeof errorData.detail === 'string') {
      return errorData.detail
    }
    
    // Fallback to 'message' field (secondary format)
    if (errorData.message && typeof errorData.message === 'string') {
      return errorData.message
    }
    
    // If neither field exists, use fallback with status text
    return `${fallbackMessage}: ${response.statusText}`
  } catch (jsonError) {
    // If JSON parsing fails, use fallback with status text
    return `${fallbackMessage}: ${response.statusText}`
  }
}

/**
 * Synchronous version for already parsed error data
 */
export function extractErrorMessage(
  errorData: any,
  fallbackMessage: string
): string {
  // First, check for 'detail' field (primary backend error format)
  if (errorData?.detail && typeof errorData.detail === 'string') {
    return errorData.detail
  }
  
  // Fallback to 'message' field (secondary format)
  if (errorData?.message && typeof errorData.message === 'string') {
    return errorData.message
  }
  
  // If neither field exists, use fallback
  return fallbackMessage
}