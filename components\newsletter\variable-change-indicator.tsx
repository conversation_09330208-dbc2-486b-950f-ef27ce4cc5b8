"use client"

import React, { useEffect, useState } from 'react'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Plus, Minus, Variable, CheckCircle, AlertCircle } from 'lucide-react'
import { cn } from '@/lib/utils'

interface VariableChange {
  type: 'added' | 'removed'
  variables: string[]
}

interface VariableChangeIndicatorProps {
  changes: VariableChange[]
  className?: string
  autoHide?: boolean
  autoHideDelay?: number
}

export function VariableChangeIndicator({ 
  changes, 
  className,
  autoHide = true,
  autoHideDelay = 3000
}: VariableChangeIndicatorProps) {
  const [visible, setVisible] = useState(false)

  useEffect(() => {
    if (changes.length > 0) {
      setVisible(true)
      
      if (autoHide) {
        const timer = setTimeout(() => {
          setVisible(false)
        }, autoHideDelay)
        
        return () => clearTimeout(timer)
      }
    }
  }, [changes, autoHide, autoHideDelay])

  if (!visible || changes.length === 0) {
    return null
  }

  const addedVariables = changes.find(c => c.type === 'added')?.variables || []
  const removedVariables = changes.find(c => c.type === 'removed')?.variables || []

  return (
    <div className={cn("space-y-2", className)}>
      {addedVariables.length > 0 && (
        <Alert className="border-green-200 bg-green-50">
          <Plus className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">
            <div className="flex items-center gap-2 flex-wrap">
              <span className="text-sm font-medium">Variables afegides:</span>
              {addedVariables.map((variable) => (
                <Badge 
                  key={variable} 
                  variant="outline" 
                  className="text-green-700 border-green-300 bg-green-100"
                >
                  <Variable className="h-3 w-3 mr-1" />
                  {variable}
                </Badge>
              ))}
            </div>
          </AlertDescription>
        </Alert>
      )}
      
      {removedVariables.length > 0 && (
        <Alert className="border-orange-200 bg-orange-50">
          <Minus className="h-4 w-4 text-orange-600" />
          <AlertDescription className="text-orange-800">
            <div className="flex items-center gap-2 flex-wrap">
              <span className="text-sm font-medium">Variables eliminades:</span>
              {removedVariables.map((variable) => (
                <Badge 
                  key={variable} 
                  variant="outline" 
                  className="text-orange-700 border-orange-300 bg-orange-100"
                >
                  <Variable className="h-3 w-3 mr-1" />
                  {variable}
                </Badge>
              ))}
            </div>
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}

interface VariableStatusIndicatorProps {
  totalVariables: number
  missingVariables?: string[]
  unusedVariables?: string[]
  className?: string
}

export function VariableStatusIndicator({
  totalVariables,
  missingVariables = [],
  unusedVariables = [],
  className
}: VariableStatusIndicatorProps) {
  const hasIssues = missingVariables.length > 0 || unusedVariables.length > 0

  return (
    <div className={cn("flex items-center gap-2 text-xs", className)}>
      <div className="flex items-center gap-1">
        <Variable className="h-3 w-3" />
        <span>{totalVariables} variables</span>
      </div>
      
      {hasIssues ? (
        <AlertCircle className="h-3 w-3 text-orange-500" />
      ) : (
        <CheckCircle className="h-3 w-3 text-green-500" />
      )}
      
      {missingVariables.length > 0 && (
        <Badge variant="destructive" className="text-xs px-1 py-0">
          {missingVariables.length} sense definir
        </Badge>
      )}
      
      {unusedVariables.length > 0 && (
        <Badge variant="secondary" className="text-xs px-1 py-0">
          {unusedVariables.length} no utilitzades
        </Badge>
      )}
    </div>
  )
}
