server {
    listen 80;
    server_name pre.newsletter.grandvaliraresorts.com;

    # ssl_certificate /etc/nginx/ssl/grandvaliraresorts/fullchain.pem; # (or bundle.crt)
    # ssl_certificate_key /etc/nginx/ssl/grandvaliraresorts/grandvaliraresorts.key;

    access_log /var/log/nginx/pre.access.log;
    error_log /var/log/nginx/pre.error.log;

    # Django API
    location ^~ /apiv1/ {
        proxy_pass http://127.0.0.1:8001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_connect_timeout 120s;
        proxy_read_timeout 120s;
        proxy_send_timeout 120s;
    }

    # Static files
    location /static/ {
        alias /var/www/grandvalira-newsletters-back-pre/staticfiles/;
        autoindex on;
    }

    # Media files
    location /media/ {
        alias /var/www/grandvalira-newsletters-back-pre/media/;
        autoindex on;
    }

    # Next.js Frontend
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Custom 502 error page
    error_page 502 = /mantenimiento.html;
    location = /mantenimiento.html {
        root /var/www/html;
        internal;
    }
}

# Commented out HTTPS redirect since we're using HTTP only
# server {
#     listen 80;
#     server_name pre.newsletter.grandvaliraresorts.com;
#     return 301 https://$host$request_uri;
# }