"use client"

import React, { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { useSession } from "next-auth/react"
import { ArrowLeft, Save, Eye, Variable, Globe, Tag, Copy, Check, SparklesIcon } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { HtmlEditor } from "@/components/ui/html-editor"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { toast } from "sonner"
import { useCreateHeaderFooter } from "@/hooks/use-create-header-footer"
import { useBrands } from "@/hooks/use-brands"
import { useVariableTypes } from "@/hooks/use-variable-types"

interface HeaderFooterVariable {
  name: string
  variable_type_id: string
  default_value: {
    es: string
    ca: string
    fr: string
    en: string
  }
}

interface HeaderFooterFormData {
  name: string
  brand: string
  element_type: 'header' | 'footer'
  html_content: string
  variables: HeaderFooterVariable[]
  is_active: boolean
}

export default function CreateHeaderFooterPage() {
  const router = useRouter()
  const { status } = useSession()
  const { createHeaderFooter, loading: createLoading, error: createError } = useCreateHeaderFooter()
  const { brands } = useBrands()
  const { variableTypes } = useVariableTypes()

  const [formData, setFormData] = useState<HeaderFooterFormData>({
    name: "",
    brand: "",
    element_type: "header",
    html_content: "",
    variables: [],
    is_active: true,
  })

  const [previewHtml, setPreviewHtml] = useState("")
  const [selectedLanguage, setSelectedLanguage] = useState<'es' | 'ca' | 'fr' | 'en'>('es')
  const [showConfirmDialog, setShowConfirmDialog] = useState(false)
  const [copySuccess, setCopySuccess] = useState(false)

  // Extract variables from HTML content (auto-detection)
  useEffect(() => {
    const variableRegex = /\{\{\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*\}\}/g
    const foundVariables = new Set<string>()
    let match

    while ((match = variableRegex.exec(formData.html_content)) !== null) {
      foundVariables.add(match[1])
    }

    // Update variables list
    const newVariables: HeaderFooterVariable[] = Array.from(foundVariables).map(varName => {
      const existing = formData.variables.find(v => v.name === varName)
      return existing || {
        name: varName,
        variable_type_id: variableTypes.length > 0 ? variableTypes[0].id : "",
        default_value: { es: '', ca: '', fr: '', en: '' }
      }
    })

    setFormData(prev => ({ ...prev, variables: newVariables }))
  }, [formData.html_content, variableTypes])

  // Update preview HTML when content, variables, or selected language change
  useEffect(() => {
    let html = formData.html_content

    // Replace variables in HTML with their values in the selected language
    formData.variables.forEach((variable) => {
      const regex = new RegExp(`\\{\\{\\s*${variable.name}\\s*\\}\\}`, 'g')
      html = html.replace(regex, variable.default_value[selectedLanguage] || '')
    })

    setPreviewHtml(html)
  }, [formData.html_content, formData.variables, selectedLanguage])

  // Helper functions for variables
  const getVariableTypeName = (variableTypeId: string) => {
    const variableType = variableTypes.find(type => type.id === variableTypeId)
    return variableType ? variableType.display_name : 'Unknown Type'
  }

  const updateVariable = (index: number, field: keyof HeaderFooterVariable, value: any) => {
    setFormData(prev => ({
      ...prev,
      variables: prev.variables.map((variable, i) =>
        i === index ? { ...variable, [field]: value } : variable
      )
    }))
  }

  const updateVariableLanguage = (index: number, language: 'es' | 'ca' | 'fr' | 'en', value: string) => {
    setFormData(prev => ({
      ...prev,
      variables: prev.variables.map((variable, i) =>
        i === index
          ? {
            ...variable,
            default_value: {
              ...variable.default_value,
              [language]: value
            }
          }
          : variable
      )
    }))
  }

  const validateForm = () => {
    if (!formData.name.trim()) {
      toast.error('El nom de la capçalera/peu és obligatori')
      return false
    }

    if (!formData.brand) {
      toast.error('Si us plau, selecciona una marca')
      return false
    }



    if (!formData.html_content.trim()) {
      toast.error('El contingut HTML és obligatori')
      return false
    }

    return true
  }

  const copyHtmlToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(previewHtml)
      setCopySuccess(true)
      toast.success('HTML copiat al portapapers!')
      setTimeout(() => setCopySuccess(false), 2000)
    } catch (error) {
      console.error('Error copying to clipboard:', error)
      toast.error('Error copiant al portapapers')
    }
  }

  const handleSubmit = () => {
    if (validateForm()) {
      setShowConfirmDialog(true)
    }
  }

  const confirmCreate = async () => {
    try {
      await createHeaderFooter(formData)
      toast.success('Capçalera/Peu creat amb èxit!')
      setShowConfirmDialog(false)
      router.push('/blocks/header-footer')
    } catch (error) {
      console.error('Error creating header/footer:', error)
      setShowConfirmDialog(false)
    }
  }

  const loadHeaderExample = () => {
    const exampleHtml = `
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px 0; text-align: center; color: white; font-family: Arial, sans-serif;">
        <div style="max-width: 600px; margin: 0 auto; padding: 0 20px;">
            <h1 style="margin: 0; font-size: 28px; font-weight: bold; text-shadow: 0 2px 4px rgba(0,0,0,0.3);">
                Benvingut a {{ company_name }}
            </h1>
            <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">
                {{ header_subtitle }}
            </p>
        </div>
    </div>`

    setFormData(prev => ({
      ...prev,
      name: "Capçalera d'Exemple",
      html_content: exampleHtml
    }))
  }

  const loadFooterExample = () => {
    const exampleHtml = `
    <div style="background-color: #2c3e50; color: #ecf0f1; padding: 30px 20px; font-family: Arial, sans-serif; text-align: center;">
        <div style="max-width: 600px; margin: 0 auto;">
            <div style="border-bottom: 1px solid #34495e; padding-bottom: 20px; margin-bottom: 20px;">
                <h3 style="margin: 0 0 10px 0; font-size: 20px; color: #3498db;">
                    Mantén-te Connectat
                </h3>
                <div style="margin: 15px 0;">
                    <a href="{{ facebook_url }}" style="color: #3498db; text-decoration: none; margin: 0 10px; font-size: 14px;">Facebook</a>
                    <a href="{{ twitter_url }}" style="color: #3498db; text-decoration: none; margin: 0 10px; font-size: 14px;">Twitter</a>
                    <a href="{{ instagram_url }}" style="color: #3498db; text-decoration: none; margin: 0 10px; font-size: 14px;">Instagram</a>
                    <a href="{{ linkedin_url }}" style="color: #3498db; text-decoration: none; margin: 0 10px; font-size: 14px;">LinkedIn</a>
                </div>
            </div>
            <div style="font-size: 12px; color: #95a5a6; line-height: 1.5;">
                <p style="margin: 0 0 10px 0;">
                    © {{ current_year }} {{ company_name }}. Tots els drets reservats.
                </p>
                <p style="margin: 0;">
                    Has rebut aquest correu perquè t'has subscrit al nostre butlletí.
                    <a href="{{ unsubscribe_url }}" style="color: #3498db; text-decoration: none;">Donar-se de baixa</a>
                </p>
            </div>
        </div>
    </div>`

    setFormData(prev => ({
      ...prev,
      name: "Peu d'Exemple",
      html_content: exampleHtml
    }))
  }

  const isVariableTypeAI = (variableTypeId: string) => {
    const variableType = variableTypes.find(type => type.id === variableTypeId)
    return variableType ? variableType.ai_generated : false
  }

  // Show loading while session is being fetched
  if (status === "loading") {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-32">
          <div className="text-muted-foreground">Carregant...</div>
        </div>
      </div>
    )
  }

  // Redirect to login if not authenticated
  if (status === "unauthenticated") {
    router.push('/login')
    return null
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Crear Capçalera/Peu</h1>
          <p className="text-muted-foreground">
            Crea una nova capçalera o peu per a les newsletters
          </p>
        </div>
        <Button onClick={handleSubmit} disabled={createLoading}>
          <Save className="mr-2 h-4 w-4" />
          {createLoading ? 'Creant...' : 'Crear Capçalera/Peu'}
        </Button>
      </div>



      {/* Error Display */}
      {createError && (
        <div className="p-4 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
          Error: {createError}
        </div>
      )}

      {/* Header/Footer Details */}
      <Card>
        <CardHeader>
          <CardTitle>Detalls de la Capçalera/Peu</CardTitle>
          <CardDescription>
            Configura els paràmetres de la teva capçalera/peu
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="brand">Marca</Label>
              <Select
                value={formData.brand}
                onValueChange={(value) => setFormData(prev => ({ ...prev, brand: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecciona una marca" />
                </SelectTrigger>
                <SelectContent>
                  {brands.map((brand) => (
                    <SelectItem key={brand.id} value={brand.id}>
                      {brand.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="name-header">Nom</Label>
              <Input
                id="name"
                placeholder="Introdueix el nom de la capçalera/peu"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value.substring(0, 70) }))}
                maxLength={70}
              />
              <p className="text-xs text-muted-foreground">{formData.name.length}/70 caràcters</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="element_type">Tipus</Label>
              <Select
                value={formData.element_type}
                onValueChange={(value: 'header' | 'footer') => setFormData(prev => ({ ...prev, element_type: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecciona el tipus" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="header">Capçalera</SelectItem>
                  <SelectItem value="footer">Peu</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="mt-6">
            <div className="flex items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <Label className="text-base">Actiu</Label>
                <p className="text-sm text-muted-foreground">
                  Activa aquesta capçalera/peu per fer-la disponible per al seu ús
                </p>
              </div>
              <Switch
                checked={formData.is_active}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: checked }))}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* HTML Editor and Preview - Side by Side */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Left Panel - HTML Editor and Variables */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Contingut HTML</CardTitle>
                  <CardDescription>
                    Escriu el teu contingut HTML per a la capçalera/peu. Utilitza {`{{ nomVariable }}`} per a variables.
                  </CardDescription>
                </div>
                <div className="flex gap-2">
                  {/* <Button
                    variant="outline"
                    size="sm"
                    onClick={loadHeaderExample}
                    disabled={formData.element_type !== 'header'}
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Exemple Capçalera
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={loadFooterExample}
                    disabled={formData.element_type !== 'footer'}
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Exemple Peu
                  </Button> */}
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <HtmlEditor
                placeholder="Introdueix el teu contingut HTML aquí..."
                value={formData.html_content}
                onChange={(value) => setFormData(prev => ({ ...prev, html_content: value }))}
                rows={20}
              />
            </CardContent>
          </Card>

          {/* Variables Section */}
          {formData.variables.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Variable className="h-5 w-5" />
                  Variables de la Capçalera/Peu
                </CardTitle>
                <CardDescription>
                  Configura els valors per defecte per a les variables trobades al teu HTML en tots els idiomes
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-8">
                  {formData.variables.map((variable, index) => (
                    <div key={variable.name} className="relative">
                      {/* Variable Header */}
                      <div className="flex items-center justify-between mb-6 pb-4 border-b">
                        <div className="flex items-center gap-3">
                          <div className="flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-600 rounded-full text-sm font-medium">
                            {index + 1}
                          </div>
                          <div>
                            <h6 className="font-semibold text-gray-900 h-6">
                              {variable.name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                            </h6>
                            <p className="text-sm text-gray-500 font-mono">
                              {`{{ ${variable.name} }}`}
                            </p>
                            <div className="flex items-center gap-1 mt-1">
                              <Tag className="h-3 w-3 text-blue-500" />
                              <span className="text-xs text-blue-600 font-medium flex items-center gap-1">
                                {isVariableTypeAI(variable.variable_type_id) && (
                                  <SparklesIcon className="h-3 w-3 text-yellow-500" />
                                )}
                                {getVariableTypeName(variable.variable_type_id)}
                              </span>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-3">
                          <div className="flex items-center gap-2">
                            <Label className="text-sm text-gray-600">Tipus de Variable</Label>
                          </div>
                          <Select
                            value={variable.variable_type_id}
                            onValueChange={(value) => updateVariable(index, 'variable_type_id', value)}
                          >
                            <SelectTrigger className="w-48">
                              <SelectValue placeholder="Selecciona tipus" />
                            </SelectTrigger>
                            <SelectContent>
                              {variableTypes.map((type) => (
                                <SelectItem key={type.id} value={type.id}>
                                  <div className="flex flex-col">
                                    <span className="font-medium text-left">{type.display_name}</span>
                                    <span className="text-xs text-muted-foreground text-left flex items-center gap-1">
                                      {type.ai_generated && <SparklesIcon className="h-3 w-3 text-yellow-500" />}
                                      {type.field_type_display}
                                    </span>
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      {/* Language Inputs */}
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <div className="space-y-3">
                          <div className="flex items-center gap-2">
                            <Globe className="h-4 w-4 text-red-500" />
                            <Label htmlFor={`var-${variable.name}-es`} className="font-medium">
                              Espanyol
                            </Label>
                            <span className="text-xs bg-red-100 text-red-700 px-2 py-1 rounded">ES</span>
                          </div>
                          <Input
                            id={`var-${variable.name}-es`}
                            placeholder="Introdueix el valor en espanyol"
                            value={variable.default_value.es}
                            onChange={(e) => updateVariableLanguage(index, 'es', e.target.value)}
                            className="border-red-200 focus:border-red-400"
                          />
                        </div>

                        <div className="space-y-3">
                          <div className="flex items-center gap-2">
                            <Globe className="h-4 w-4 text-yellow-600" />
                            <Label htmlFor={`var-${variable.name}-ca`} className="font-medium">
                              Català
                            </Label>
                            <span className="text-xs bg-yellow-100 text-yellow-700 px-2 py-1 rounded">CA</span>
                          </div>
                          <Input
                            id={`var-${variable.name}-ca`}
                            placeholder="Introdueix el valor en català"
                            value={variable.default_value.ca}
                            onChange={(e) => updateVariableLanguage(index, 'ca', e.target.value)}
                            className="border-yellow-200 focus:border-yellow-400"
                          />
                        </div>

                        <div className="space-y-3">
                          <div className="flex items-center gap-2">
                            <Globe className="h-4 w-4 text-blue-500" />
                            <Label htmlFor={`var-${variable.name}-fr`} className="font-medium">
                              Francès
                            </Label>
                            <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">FR</span>
                          </div>
                          <Input
                            id={`var-${variable.name}-fr`}
                            placeholder="Introdueix el valor en francès"
                            value={variable.default_value.fr}
                            onChange={(e) => updateVariableLanguage(index, 'fr', e.target.value)}
                            className="border-blue-200 focus:border-blue-400"
                          />
                        </div>

                        <div className="space-y-3">
                          <div className="flex items-center gap-2">
                            <Globe className="h-4 w-4 text-green-500" />
                            <Label htmlFor={`var-${variable.name}-en`} className="font-medium">
                              Anglès
                            </Label>
                            <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">EN</span>
                          </div>
                          <Input
                            id={`var-${variable.name}-en`}
                            placeholder="Introdueix el valor en anglès"
                            value={variable.default_value.en}
                            onChange={(e) => updateVariableLanguage(index, 'en', e.target.value)}
                            className="border-green-200 focus:border-green-400"
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Right Panel - Preview */}
        <Card className="sticky top-6 max-h-[calc(100vh-48px)] overflow-y-auto">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  Vista prèvia
                </CardTitle>
                <CardDescription>
                  Veu com es veurà la teva capçalera/peu.
                </CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <Select value={selectedLanguage} onValueChange={(value) => setSelectedLanguage(value as 'es' | 'ca' | 'fr' | 'en')}>
                  <SelectTrigger className="w-[140px]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="es">
                      <div className="flex items-center gap-2">
                        <Globe className="h-3 w-3 text-red-500" />
                        <span>Espanyol</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="ca">
                      <div className="flex items-center gap-2">
                        <Globe className="h-3 w-3 text-yellow-600" />
                        <span>Català</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="fr">
                      <div className="flex items-center gap-2">
                        <Globe className="h-3 w-3 text-blue-500" />
                        <span>Francès</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="en">
                      <div className="flex items-center gap-2">
                        <Globe className="h-3 w-3 text-green-500" />
                        <span>Anglès</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
                {/* <Button
                  variant="outline"
                  size="sm"
                  onClick={copyHtmlToClipboard}
                  disabled={!previewHtml}
                  className="flex items-center gap-2"
                >
                  {copySuccess ? (
                    <>
                      <Check className="h-4 w-4 text-green-600" />
                      <span>Copiat!</span>
                    </>
                  ) : (
                    <>
                      <Copy className="h-4 w-4" />
                      <span>Copiar HTML</span>
                    </>
                  )}
                </Button> */}
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="border rounded-lg p-6 min-h-[500px] bg-white shadow-inner">
              {previewHtml ? (
                <div dangerouslySetInnerHTML={{ __html: previewHtml }} />
              ) : (
                <div className="flex items-center justify-center h-full text-muted-foreground">
                  <div className="text-center">
                    <Eye className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>Comença a escriure HTML per veure la vista prèvia</p>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Confirmation Dialog */}
      <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirmar Creació de la Capçalera/Peu</DialogTitle>
            <DialogDescription>
              Estàs segur que vols crear aquesta capçalera/peu amb la següent configuració?
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">Nom:</span>
                <p className="text-muted-foreground">{formData.name}</p>
              </div>
              <div>
                <span className="font-medium">Tipus:</span>
                <p className="text-muted-foreground">
                  {formData.element_type === 'header' ? 'Capçalera' : 'Peu'}
                </p>
              </div>
              <div>
                <span className="font-medium">Marca:</span>
                <p className="text-muted-foreground">
                  {brands.find(b => b.id === formData.brand)?.name || formData.brand}
                </p>
              </div>

              <div>
                <span className="font-medium">Variables:</span>
                <p className="text-muted-foreground">
                  {formData.variables.length > 0 ? `${formData.variables.length} variable(s)` : 'Cap variable'}
                </p>
              </div>
              <div>
                <span className="font-medium">Actiu:</span>
                <span className={formData.is_active ? "text-green-600" : "text-red-600"}>
                  {formData.is_active ? 'Sí' : 'No'}
                </span>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowConfirmDialog(false)}
              disabled={createLoading}
            >
              Cancel·lar
            </Button>
            <Button
              onClick={confirmCreate}
              disabled={createLoading}
            >
              <Save className="mr-2 h-4 w-4" />
              {createLoading ? 'Creant...' : 'Crear Capçalera/Peu'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
