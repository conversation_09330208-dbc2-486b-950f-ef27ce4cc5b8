import { useState, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { NewsletterBlock, NewsletterBlockVariable } from '@/types/newsletter'
import { DEFAULT_API_URL } from '@/constants/constants'
import { extractApiErrorMessage } from '@/lib/api-error-utils'

interface TranslateBlockRequest {
  block_id: string
  source_language: string
  variables: Record<string, string>
  user_input?: string
}

interface TranslateBlockResponse extends NewsletterBlock {
  // The response is the full block with translated content
}

interface UseTranslateBlockApiReturn {
  translateBlock: (request: TranslateBlockRequest) => Promise<TranslateBlockResponse>
  loading: boolean
  error: string | null
}

const API_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_URL || DEFAULT_API_URL

export function useTranslateBlockApi(): UseTranslateBlockApiReturn {
  const { data: session } = useSession()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const getAuthHeaders = useCallback(() => {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    }

    if (session?.djangoAccessToken) {
      headers['Authorization'] = `Bearer ${session.djangoAccessToken}`
    }

    return headers
  }, [session?.djangoAccessToken])

  const translateBlock = useCallback(async (request: TranslateBlockRequest): Promise<TranslateBlockResponse> => {
    if (!session?.djangoAccessToken) {
      throw new Error('No authentication token available')
    }

    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`${API_BASE_URL}/openai/traduce-block/`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify(request)
      })

      if (!response.ok) {
        const errorMessage = await extractApiErrorMessage(response, 'Failed to translate block')
        throw new Error(errorMessage)
      }

      const data: TranslateBlockResponse = await response.json()
      return data
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while translating block'
      setError(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }, [session?.djangoAccessToken, getAuthHeaders])

  return {
    translateBlock,
    loading,
    error
  }
}

// Helper function to extract variables from a block for a specific language
export function extractVariables(block: NewsletterBlock, sourceLanguage: string): Record<string, string> {
  if (!block.variable_values || block.variable_values.length === 0) {
    return {}
  }

  const variables: Record<string, string> = {}

  block.variable_values.forEach(variable => {
    const value = variable.value[sourceLanguage as keyof typeof variable.value]
    if (value && value.trim() !== '') {
      variables[variable.name] = value
    }
  })

  return variables
}


