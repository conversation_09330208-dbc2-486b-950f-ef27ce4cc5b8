"use client"

import { MailIcon, PlusCircleIcon, type LucideIcon } from "lucide-react"

import { Button } from "@/components/ui/button"
import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { useUserPermissions, PERMISSION_CODENAMES } from "@/hooks/use-user-permissions"

export function NavMain({
  items,
}: {
  items: {
    title: string
    url: string
    icon?: LucideIcon
  }[]
}) {
  const pathname = usePathname()
  const { hasPermission } = useUserPermissions()

  return (
    <SidebarGroup>
      <SidebarGroupContent className="flex flex-col gap-2">
        {hasPermission(PERMISSION_CODENAMES.CREATE_EDIT_NEWSLETTERS) && (
          <SidebarMenu>
            <SidebarMenuItem className="">
              <Link href="/newsletter/create" className="p-0 m-0 flex items-center gap-2">
                <SidebarMenuButton
                  tooltip="Crear newsletter"
                  className="min-w-8 bg-primary text-primary-foreground duration-200 ease-linear hover:bg-primary/90 hover:text-primary-foreground active:bg-primary/90 active:text-primary-foreground"
                >
                  <PlusCircleIcon />
                  <span>Crear newsletter</span>
                </SidebarMenuButton>
                <Button
                  size="icon"
                  className="h-9 w-9 shrink-0 group-data-[collapsible=icon]:opacity-0"
                  variant="outline"
                >
                  <MailIcon />
                  <span className="sr-only">Inbox</span>
                </Button>
              </Link>
            </SidebarMenuItem>
          </SidebarMenu>
        )}
        <SidebarMenu>
          {items.map((item, index) => (
            <SidebarMenuItem key={item.title}>
              <Link href={item.url}>
                <SidebarMenuButton tooltip={item.title} isActive={item.url === pathname}>
                  {item.icon && <item.icon />}
                  <span>{item.title}</span>
                </SidebarMenuButton>
              </Link>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  )
}

import {
  Blocks,
  FileText,
  LayoutTemplate,
  Newspaper,
  Settings,
} from 'lucide-react'
import { NavSecondary } from './nav-secondary'

export function AppNavMain() {
  const { hasPermission } = useUserPermissions()

  const navItems = [
    {
      title: 'Plantilles',
      icon: LayoutTemplate,
      url: '/templates',
      permission: PERMISSION_CODENAMES.VIEW_MANAGE_TEMPLATES,
    },
    {
      title: 'Capçaleres i peus',
      icon: Blocks,
      url: '/blocks',
      permission: PERMISSION_CODENAMES.VIEW_MANAGE_HEADERS_FOOTERS,
    },
    {
      title: 'Newsletters',
      icon: Newspaper,
      url: '/newsletters',
      permission: PERMISSION_CODENAMES.VIEW_NEWSLETTERS,
    },
    {
      title: 'Documents',
      icon: FileText,
      url: '/documents',
      permission: null, // Always visible for now
    },
    {
      title: 'Configuració',
      icon: Settings,
      url: '/configuration',
      permission: PERMISSION_CODENAMES.VIEW_MANAGE_USERS_ROLES,
    },
  ].filter(item => !item.permission || hasPermission(item.permission))

  return (
    <NavSecondary items={navItems} />
  )
}
