import { useState, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { NewsletterBlock, NewsletterHeaderFooter, NewsletterBuilderData } from '@/types/newsletter'
import { DEFAULT_API_URL } from '@/constants/constants'
import { extractApiErrorMessage } from '@/lib/api-error-utils'

interface AIRequest {
  prompt?: string
  language: string
  context: {
    type: 'block' | 'general' | 'header_footer'
    blockId?: string
    newsletterId?: string
    block?: NewsletterBlock
    headerFooter?: NewsletterHeaderFooter
    debugInfo?: Record<string, any>
  }
}

interface AIResponse {
  content: string
  language: string
  blockId?: string | null
  type: 'block' | 'general' | 'header_footer'
  originalPrompt?: string
}

interface GenerateBlockContentRequest {
  language: string
  prompt: string
  variables: Record<string, string>
}

interface GenerateBlockContentResponse {
  [key: string]: string // Dynamic keys based on variable names
}

interface GenerateNewsletterContentRequest {
  newsletter_id: string
  language: string
  prompt: string
}

interface GenerateNewsletterContentResponse {
  newsletter: NewsletterBuilderData
  language: string
  prompt: string
}

interface UseAIApiReturn {
  generateContent: (request: AIRequest) => Promise<AIResponse>
  generateBlockContent: (request: GenerateBlockContentRequest) => Promise<GenerateBlockContentResponse>
  generateNewsletterContent: (request: GenerateNewsletterContentRequest) => Promise<GenerateNewsletterContentResponse>
  loading: boolean
  error: string | null
}

const API_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_URL || DEFAULT_API_URL

export function useAIApi(): UseAIApiReturn {
  const { data: session } = useSession()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const getAuthHeaders = useCallback(() => {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    }

    if (session?.djangoAccessToken) {
      headers['Authorization'] = `Bearer ${session.djangoAccessToken}`
    }

    return headers
  }, [session?.djangoAccessToken])

  const generateContent = useCallback(async (request: AIRequest): Promise<AIResponse> => {
    if (!session?.djangoAccessToken) {
      throw new Error('No authentication token available')
    }

    setLoading(true)
    setError(null)

    try {
      // For now, simulate API response since the actual AI endpoint may not be implemented yet
      // TODO: Replace with actual AI API endpoint when available

      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1500 + Math.random() * 1000))

      // Generate mock response based on context
      let mockContent = ''

      if (request.context.type === 'block' && request.context.block) {
        mockContent = generateMockBlockContent(request.context.block, request.prompt, request.language)
      } else if (request.context.type === 'header_footer' && request.context.headerFooter) {
        mockContent = generateMockHeaderFooterContent(request.context.headerFooter, request.prompt, request.language)
      } else {
        mockContent = generateMockGeneralContent(request.prompt, request.language)
      }

      const response: AIResponse = {
        content: mockContent,
        language: request.language,
        blockId: request.context.blockId || null,
        type: request.context.type,
        originalPrompt: request.prompt
      }

      return response

      // TODO: Uncomment and implement when actual AI API is available
      /*
      const response = await fetch(`${API_BASE_URL}/ai/generate-content/`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify(request)
      })

      if (!response.ok) {
        const errorMessage = await extractApiErrorMessage(response, 'Failed to generate AI content')
        throw new Error(errorMessage)
      }

      const data: AIResponse = await response.json()
      return data
      */
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while generating content'
      setError(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }, [session?.djangoAccessToken, getAuthHeaders])

  const generateBlockContent = useCallback(async (request: GenerateBlockContentRequest): Promise<GenerateBlockContentResponse> => {
    if (!session?.djangoAccessToken) {
      throw new Error('No authentication token available')
    }

    setLoading(true)
    setError(null)

    try {
      console.log('Generating block content with request:', request)
      console.log('API URL:', `${API_BASE_URL}/openai/generate-block-content/`)

      const response = await fetch(`${API_BASE_URL}/openai/generate-block-content/`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify(request)
      })

      if (!response.ok) {
        const errorMessage = await extractApiErrorMessage(response, 'Failed to generate block content')
        throw new Error(errorMessage)
      }

      const data: GenerateBlockContentResponse = await response.json()
      console.log('Block content generation response:', data)
      return data
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while generating block content'
      console.error('Error generating block content:', err)
      setError(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }, [session?.djangoAccessToken, getAuthHeaders])

  const generateNewsletterContent = useCallback(async (request: GenerateNewsletterContentRequest): Promise<GenerateNewsletterContentResponse> => {
    if (!session?.djangoAccessToken) {
      throw new Error('No authentication token available')
    }

    setLoading(true)
    setError(null)

    try {
      console.log('Generating newsletter content with request:', request)
      console.log('API URL:', `${API_BASE_URL}/openai/generate-newsletter-content/`)

      const response = await fetch(`${API_BASE_URL}/openai/generate-newsletter-content/`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify(request)
      })

      if (!response.ok) {
        const errorMessage = await extractApiErrorMessage(response, 'Failed to generate newsletter content')
        throw new Error(errorMessage)
      }

      const newsletterData: NewsletterBuilderData = await response.json()
      console.log('Newsletter content generation response:', newsletterData)

      const result: GenerateNewsletterContentResponse = {
        newsletter: newsletterData,
        language: request.language,
        prompt: request.prompt
      }

      return result
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while generating newsletter content'
      console.error('Error generating newsletter content:', err)
      setError(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }, [session?.djangoAccessToken, getAuthHeaders])

  return {
    generateContent,
    generateBlockContent,
    generateNewsletterContent,
    loading,
    error
  }
}

// Mock content generators for demonstration
function generateMockBlockContent(block: NewsletterBlock, prompt?: string, language: string = 'ca'): string {
  const translations = {
    ca: {
      title: 'Contingut generat per IA',
      content: `Aquest és contingut generat automàticament per al bloc "${block.name}".`,
      prompt: prompt ? `Basat en les instruccions: "${prompt}"` : 'Generat amb context automàtic.'
    },
    es: {
      title: 'Contenido generado por IA',
      content: `Este es contenido generado automáticamente para el bloque "${block.name}".`,
      prompt: prompt ? `Basado en las instrucciones: "${prompt}"` : 'Generado con contexto automático.'
    },
    fr: {
      title: 'Contenu généré par IA',
      content: `Ceci est du contenu généré automatiquement pour le bloc "${block.name}".`,
      prompt: prompt ? `Basé sur les instructions: "${prompt}"` : 'Généré avec un contexte automatique.'
    },
    en: {
      title: 'AI Generated Content',
      content: `This is automatically generated content for the block "${block.name}".`,
      prompt: prompt ? `Based on instructions: "${prompt}"` : 'Generated with automatic context.'
    }
  }

  const t = translations[language as keyof typeof translations] || translations.ca

  return `
    <div style="padding: 20px; border: 1px solid #e2e8f0; border-radius: 8px; background: #f8fafc;">
      <h3 style="color: #1e293b; margin-bottom: 12px;">${t.title}</h3>
      <p style="color: #475569; margin-bottom: 8px;">${t.content}</p>
      <p style="color: #64748b; font-size: 14px; font-style: italic;">${t.prompt}</p>
      <div style="margin-top: 16px; padding: 12px; background: #e0f2fe; border-radius: 4px;">
        <p style="color: #0369a1; font-size: 14px; margin: 0;">
          Variables del bloc: ${block.variable_values?.length || 0} | 
          Posició: ${block.order_position} | 
          Visible: ${block.is_visible ? 'Sí' : 'No'}
        </p>
      </div>
    </div>
  `
}

function generateMockHeaderFooterContent(headerFooter: NewsletterHeaderFooter, prompt?: string, language: string = 'ca'): string {
  const translations = {
    ca: {
      title: 'Capçalera/Peu generat per IA',
      content: `Contingut generat per a "${headerFooter.name}".`
    },
    es: {
      title: 'Cabecera/Pie generado por IA',
      content: `Contenido generado para "${headerFooter.name}".`
    },
    fr: {
      title: 'En-tête/Pied généré par IA',
      content: `Contenu généré pour "${headerFooter.name}".`
    },
    en: {
      title: 'AI Generated Header/Footer',
      content: `Generated content for "${headerFooter.name}".`
    }
  }

  const t = translations[language as keyof typeof translations] || translations.ca

  return `
    <div style="padding: 16px; background: #f1f5f9; border-radius: 6px;">
      <h4 style="color: #334155; margin-bottom: 8px;">${t.title}</h4>
      <p style="color: #64748b;">${t.content}</p>
      ${prompt ? `<p style="color: #94a3b8; font-size: 13px; margin-top: 8px;">Instruccions: "${prompt}"</p>` : ''}
    </div>
  `
}

function generateMockGeneralContent(prompt?: string, language: string = 'ca'): string {
  const translations = {
    ca: {
      title: 'Contingut general generat per IA',
      content: 'Aquest contingut ha estat generat per millorar el butlletí en general.'
    },
    es: {
      title: 'Contenido general generado por IA',
      content: 'Este contenido ha sido generado para mejorar el boletín en general.'
    },
    fr: {
      title: 'Contenu général généré par IA',
      content: 'Ce contenu a été généré pour améliorer la newsletter en général.'
    },
    en: {
      title: 'General AI Generated Content',
      content: 'This content has been generated to improve the newsletter overall.'
    }
  }

  const t = translations[language as keyof typeof translations] || translations.ca

  return `
    <div style="padding: 24px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 8px;">
      <h2 style="margin-bottom: 16px;">${t.title}</h2>
      <p style="margin-bottom: 12px;">${t.content}</p>
      ${prompt ? `<p style="opacity: 0.9; font-size: 14px;">Instruccions: "${prompt}"</p>` : ''}
    </div>
  `
}
