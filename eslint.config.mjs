export default {
  root: true,
  env: {
    browser: true,
    node: true,
    es2021: true,
  },
  parserOptions: {
    parser: '@typescript-eslint/parser',
    sourceType: 'module',
    ecmaVersion: 2021,
    project: './tsconfig.json',
  },
  extends: [
    'eslint:recommended',
    'plugin:@typescript-eslint/recommended',
    'plugin:@typescript-eslint/recommended-requiring-type-checking',
    'plugin:react/recommended',
    'plugin:react-hooks/recommended',
    'plugin:jsx-a11y/recommended',
    'plugin:import/recommended',
    'plugin:import/typescript',
    'next/core-web-vitals',
    'prettier', // Disables conflicting ESLint rules with <PERSON><PERSON><PERSON>
  ],
  plugins: [
    '@typescript-eslint',
    'react',
    'react-hooks',
    'jsx-a11y',
    'import',
  ],
  settings: {
    react: {
      version: 'detect',
    },
    'import/resolver': {
      typescript: {
        alwaysTryTypes: true,
        project: './tsconfig.json',
      },
    },
  },
  rules: {
    // General JavaScript/TypeScript rules
    'no-console': ['warn', { allow: ['warn', 'error'] }], // Allow console.warn/error
    'no-duplicate-imports': 'error', // Prevent duplicate imports
    'no-undef': 'off', // TypeScript handles this
    '@typescript-eslint/no-explicit-any': 'warn', // Warn on 'any' but don't block it
    '@typescript-eslint/explicit-module-boundary-types': 'off', // Too restrictive for Next.js
    '@typescript-eslint/no-unsafe-assignment': 'warn', // Be lenient with unsafe assignments
    '@typescript-eslint/no-unsafe-call': 'warn', // Be lenient with unsafe calls
    '@typescript-eslint/no-unsafe-member-access': 'warn', // Be lenient with unsafe member access
    '@typescript-eslint/no-unsafe-return': 'off', // Too restrictive for dynamic Next.js code
    '@typescript-eslint/no-unused-vars': 'off', // Disable as TypeScript handles this better

    // React/JSX rules
    'react/prop-types': 'off', // TypeScript handles prop types
    'react/jsx-key': 'error', // Enforce keys in JSX lists
    'react-hooks/rules-of-hooks': 'error', // Enforce React Hooks rules
    'react-hooks/exhaustive-deps': 'warn', // Warn on missing dependencies in useEffect
    'jsx-a11y/anchor-is-valid': 'off', // Next.js Link component handles <a> tags

    // Import rules
    'import/order': [
      'error',
      {
        groups: [['builtin', 'external'], 'internal', ['parent', 'sibling', 'index']],
        'newlines-between': 'always',
        alphabetize: { order: 'asc', caseInsensitive: true },
      },
    ],
    'import/no-unresolved': 'error', // Ensure imports resolve correctly

    // Next.js specific rules
    'next/no-img-element': 'error', // Prefer Next.js Image component
    'next/no-html-link-for-pages': 'error', // Prevent raw <a> tags for page navigation

    // Style-related rules (avoiding quote restrictions)
    'quotes': 'off', // Allow both single and double quotes
    'jsx-quotes': 'off', // Allow both single and double quotes in JSX
  },
  overrides: [
    {
      files: ['*.js', '*.jsx'],
      rules: {
        '@typescript-eslint/no-var-requires': 'off', // Allow require in JS files
      },
    },
  ],
};
