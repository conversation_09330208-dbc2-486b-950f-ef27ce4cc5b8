import { NewsletterBlockVariable } from '@/types/newsletter'

/**
 * Regular expression to match variables in HTML content
 * Matches patterns like {{ variableName }} with optional whitespace
 */
export const VARIABLE_REGEX = /\{\{\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*\}\}/g

/**
 * Extract variable names from HTML content
 * @param htmlContent - The HTML content to parse
 * @returns Array of unique variable names found in the content
 */
export function extractVariableNames(htmlContent: string): string[] {
  const foundVariables = new Set<string>()
  let match

  // Reset regex lastIndex to ensure we start from the beginning
  VARIABLE_REGEX.lastIndex = 0
  
  while ((match = VARIABLE_REGEX.exec(htmlContent)) !== null) {
    foundVariables.add(match[1])
  }

  return Array.from(foundVariables)
}

/**
 * Create a new variable object with default values
 * @param name - The variable name
 * @param variableTypeId - The default variable type ID to use
 * @returns A new NewsletterBlockVariable object
 */
export function createNewVariable(
  name: string, 
  variableTypeId: string = ''
): Omit<NewsletterBlockVariable, 'id'> {
  return {
    variable_type_id: variableTypeId,
    variable_type_name: '',
    variable_type_display_name: '',
    variable_type_field_type: 'string',
    name,
    value: {
      es: '',
      ca: '',
      fr: '',
      en: ''
    }
  }
}

/**
 * Compare two arrays of variable names and return the differences
 * @param oldVariables - Array of current variable names
 * @param newVariables - Array of new variable names
 * @returns Object containing added and removed variable names
 */
export function compareVariableLists(
  oldVariables: string[], 
  newVariables: string[]
): { added: string[], removed: string[] } {
  const oldSet = new Set(oldVariables)
  const newSet = new Set(newVariables)
  
  const added = newVariables.filter(name => !oldSet.has(name))
  const removed = oldVariables.filter(name => !newSet.has(name))
  
  return { added, removed }
}

/**
 * Update a list of variables based on HTML content changes
 * @param currentVariables - Current array of NewsletterBlockVariable objects
 * @param htmlContent - New HTML content to parse
 * @param defaultVariableTypeId - Default variable type ID for new variables
 * @returns Updated array of variables
 */
export function updateVariablesFromHtml(
  currentVariables: NewsletterBlockVariable[],
  htmlContent: string,
  defaultVariableTypeId: string = ''
): NewsletterBlockVariable[] {
  const newVariableNames = extractVariableNames(htmlContent)
  const currentVariableNames = currentVariables.map(v => v.name)
  
  const { added, removed } = compareVariableLists(currentVariableNames, newVariableNames)
  
  // Remove variables that are no longer in the HTML
  let updatedVariables = currentVariables.filter(variable => 
    !removed.includes(variable.name)
  )
  
  // Add new variables found in the HTML
  const newVariables = added.map(name => ({
    ...createNewVariable(name, defaultVariableTypeId),
    id: `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}` // Temporary ID
  }))
  
  updatedVariables = [...updatedVariables, ...newVariables]
  
  return updatedVariables
}

/**
 * Validate variable name format
 * @param name - Variable name to validate
 * @returns True if the variable name is valid
 */
export function isValidVariableName(name: string): boolean {
  return /^[a-zA-Z_][a-zA-Z0-9_]*$/.test(name)
}

/**
 * Get all variables referenced in HTML content that don't exist in the current variable list
 * @param htmlContent - HTML content to check
 * @param currentVariables - Current variables list
 * @returns Array of missing variable names
 */
export function getMissingVariables(
  htmlContent: string, 
  currentVariables: NewsletterBlockVariable[]
): string[] {
  const referencedVariables = extractVariableNames(htmlContent)
  const existingVariableNames = new Set(currentVariables.map(v => v.name))
  
  return referencedVariables.filter(name => !existingVariableNames.has(name))
}

/**
 * Get all variables in the current list that are not referenced in HTML content
 * @param htmlContent - HTML content to check
 * @param currentVariables - Current variables list
 * @returns Array of unused variable objects
 */
export function getUnusedVariables(
  htmlContent: string, 
  currentVariables: NewsletterBlockVariable[]
): NewsletterBlockVariable[] {
  const referencedVariables = new Set(extractVariableNames(htmlContent))
  
  return currentVariables.filter(variable => !referencedVariables.has(variable.name))
}
