import { useState, useEffect, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { <PERSON>er<PERSON><PERSON><PERSON>, HeaderFooterResponse } from '@/types/block'
import { DEFAULT_API_URL } from '@/constants/constants'
import { extractApiErrorMessage } from '@/lib/api-error-utils'

interface UseHeadersFootersParams {
  search?: string
  brand?: string
  elementType?: string
  page?: number
  pageSize?: number
}

interface UseHeadersFootersReturn {
  headersFooters: HeaderFooter[]
  loading: boolean
  error: string | null
  totalCount: number
  hasNext: boolean
  hasPrevious: boolean
  refetch: () => void
}

export function useHeadersFooters(params: UseHeadersFootersParams = {}): UseHeadersFootersReturn {
  const { data: session } = useSession()
  const [headersFooters, setHeadersFooters] = useState<HeaderFooter[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [totalCount, setTotalCount] = useState(0)
  const [hasNext, setHasNext] = useState(false)
  const [hasPrevious, setHasPrevious] = useState(false)

  const fetchHeadersFooters = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      // Check if user is authenticated
      if (!session) {
        setError('User not authenticated')
        setHeadersFooters([])
        setTotalCount(0)
        setHasNext(false)
        setHasPrevious(false)
        return
      }

      if (!session.djangoAccessToken) {
        setError('Django access token not available. Please try logging in again.')
        setHeadersFooters([])
        setTotalCount(0)
        setHasNext(false)
        setHasPrevious(false)
        return
      }

      const queryParams = new URLSearchParams()
      if (params.search) queryParams.append('search', params.search)
      if (params.brand) queryParams.append('brand', params.brand)
      if (params.elementType) queryParams.append('element_type', params.elementType)
      if (params.page) queryParams.append('page', params.page.toString())
      if (params.pageSize) queryParams.append('page_size', params.pageSize.toString())

      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || DEFAULT_API_URL

      const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.djangoAccessToken}`
      }

      const response = await fetch(`${backendUrl}/blocks/list-headers-footers/?${queryParams.toString()}`, {
        method: 'GET',
        headers,
      })

      if (!response.ok) {
        const errorMessage = await extractApiErrorMessage(response, 'Failed to fetch headers/footers')
        throw new Error(errorMessage)
      }

      const data: HeaderFooterResponse = await response.json()

      setHeadersFooters(data.results)
      setTotalCount(data.count)
      setHasNext(!!data.next)
      setHasPrevious(!!data.previous)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
      setHeadersFooters([])
      setTotalCount(0)
      setHasNext(false)
      setHasPrevious(false)
    } finally {
      setLoading(false)
    }
  }, [params.search, params.brand, params.elementType, params.page, params.pageSize, session])

  useEffect(() => {
    fetchHeadersFooters()
  }, [fetchHeadersFooters])

  return {
    headersFooters,
    loading,
    error,
    totalCount,
    hasNext,
    hasPrevious,
    refetch: fetchHeadersFooters
  }
}
