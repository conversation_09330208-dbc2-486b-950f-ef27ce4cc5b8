"use client"

import React, { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { useSession } from "next-auth/react"
import { NextPage } from 'next'
import { Plus, Search } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { BlocksTable } from "@/components/table/blocks-table"
import { useBlocks } from "@/hooks/use-blocks"
import { useBrands } from "@/hooks/use-brands"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DEFAULT_PAGE_SIZE } from "@/constants/constants"

interface Props { }

const Page: NextPage<Props> = ({ }) => {
    const router = useRouter()
    const { status } = useSession()
    const { brands } = useBrands()
    const [blocksSearchQuery, setBlocksSearchQuery] = useState("")
    const [debouncedBlocksSearch, setDebouncedBlocksSearch] = useState("")
    const [selectedBrand, setSelectedBrand] = useState<string>("")
    const [selectedActiveStatus, setSelectedActiveStatus] = useState<string>("active")
    // Pagination state for blocks
    const [blocksPage, setBlocksPage] = useState(1)
    const [blocksPageSize, setBlocksPageSize] = useState(DEFAULT_PAGE_SIZE)

    // Debounce blocks search query
    useEffect(() => {
        const timer = setTimeout(() => {
            setDebouncedBlocksSearch(blocksSearchQuery)
        }, 300)

        return () => clearTimeout(timer)
    }, [blocksSearchQuery])

    // Reset blocks page when search, brand, or active status changes
    useEffect(() => {
        setBlocksPage(1)
    }, [debouncedBlocksSearch, selectedBrand, selectedActiveStatus])

    // Fetch blocks with search, brand, and active status filters
    const {
        blocks,
        loading: blocksLoading,
        error: blocksError,
        refetch: refetchBlocks,
        totalCount: blocksTotalCount,
        hasNext: blocksHasNext,
        hasPrevious: blocksHasPrevious
    } = useBlocks({
        search: debouncedBlocksSearch || undefined,
        brand: selectedBrand && selectedBrand !== "all" ? selectedBrand : undefined,
        isActive: selectedActiveStatus === "active" ? true : selectedActiveStatus === "inactive" ? false : undefined,
        page: blocksPage,
        pageSize: blocksPageSize
    })

    const handleCreateBlock = () => {
        router.push('/blocks/block/create')
    }

    // Show loading while session is being fetched
    if (status === "loading") {
        return (
            <div className="p-6">
                <div className="flex items-center justify-center h-32">
                    <div className="text-muted-foreground">Carregant...</div>
                </div>
            </div>
        )
    }

    // Redirect to login if not authenticated
    if (status === "unauthenticated") {
        router.push('/login')
        return null
    }

    return (
            <div className="p-6 space-y-8">
                {/* Blocks Section */}
                <div className="space-y-4">
                    <div className="flex items-center justify-between">
                        <div>
                        <h1 className="text-3xl font-bold tracking-tight">Blocs</h1>
                        <p className="text-muted-foreground">
                            Gestiona blocs de contingut reutilitzables per a les newsletters.
                        </p>
                    </div>
                    <Button onClick={handleCreateBlock}>
                        <Plus className="mr-2 h-4 w-4" />
                        Crear Bloc
                    </Button>
                </div>

                <div className="flex items-center space-x-4">
                    <div className="relative flex-1 max-w-sm">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                        <Input
                            placeholder="Cercar blocs..."
                            value={blocksSearchQuery}
                            onChange={(e) => setBlocksSearchQuery(e.target.value)}
                            className="pl-10"
                        />
                    </div>
                    <div className="space-y-2">
                        <Select value={selectedBrand} onValueChange={setSelectedBrand}>
                            <SelectTrigger className="w-[200px]">
                                <SelectValue placeholder="Totes les marques" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">Totes les marques</SelectItem>
                                {brands.map((brand) => (
                                    <SelectItem key={brand.id} value={brand.id}>
                                        {brand.name}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>
                    <div className="space-y-2">
                        <Select value={selectedActiveStatus} onValueChange={setSelectedActiveStatus}>
                            <SelectTrigger className="w-[150px]">
                                <SelectValue placeholder="Actius" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">Tots els estats</SelectItem>
                                <SelectItem value="active">Actius</SelectItem>
                                <SelectItem value="inactive">Inactius</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                </div>

                {blocksError && (
                    <div className="p-4 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
                        Error: {blocksError}
                    </div>
                )}

                <BlocksTable
                    data={blocks}
                    loading={blocksLoading}
                    onRefresh={refetchBlocks}
                    pagination={{
                        page: blocksPage,
                        pageSize: blocksPageSize,
                        totalCount: blocksTotalCount,
                        hasNext: blocksHasNext,
                        hasPrevious: blocksHasPrevious,
                        onPageChange: setBlocksPage,
                        onPageSizeChange: setBlocksPageSize
                    }}
                />
            </div>
        </div>
    )
}

export default Page