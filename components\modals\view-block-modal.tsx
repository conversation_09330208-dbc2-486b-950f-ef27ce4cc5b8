"use client"

import React, { useState, useEffect } from "react"
import { Eye, Variable, Globe, Tag, Copy, Check, SparklesIcon } from "lucide-react"
import { Block } from "@/types/block"
import { useVariableTypes } from "@/hooks/use-variable-types"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { formatDate } from "@/lib/utils"
import { Input } from "../ui/input"

interface ViewBlockModalProps {
  block: Block | null
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function ViewBlockModal({ block, open, onOpenChange }: ViewBlockModalProps) {
  const [previewHtml, setPreviewHtml] = useState("")
  const [copied, setCopied] = useState(false)
  const [selectedLanguage, setSelectedLanguage] = useState("es")
  const { variableTypes, loading: variableTypesLoading } = useVariableTypes()

  // Helper function to get variable type name
  const getVariableTypeName = (variableTypeId: string) => {
    if (variableTypesLoading) return 'Loading...'
    const variableType = variableTypes.find(type => type.id === variableTypeId)
    return variableType ? variableType.display_name : 'Unknown Type'
  }

  // Helper function to get variable type field type
  const getVariableTypeFieldType = (variableTypeId: string) => {
    if (variableTypesLoading) return ''
    const variableType = variableTypes.find(type => type.id === variableTypeId)
    return variableType ? variableType.field_type_display : ''
  }

  const isVariableTypeAI = (variableTypeId: string) => {
    if (variableTypesLoading) return false
    const variableType = variableTypes.find(type => type.id === variableTypeId)
    return variableType ? variableType.ai_generated : false
  }

  // Update preview HTML when block or language changes
  useEffect(() => {
    if (!block) return

    let html = block.html_content

    // Replace variables in HTML with their values in the selected language
    if (block.variables && Array.isArray(block.variables)) {
      block.variables.forEach((variable) => {
        const regex = new RegExp(`\\{\\{\\s*${variable.name}\\s*\\}\\}`, 'g')
        const languageValue = variable.default_value[selectedLanguage as keyof typeof variable.default_value] || ''
        html = html.replace(regex, languageValue)
      })
    }

    setPreviewHtml(html)
  }, [block, selectedLanguage])

  // Copy HTML to clipboard
  const copyHtmlToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(previewHtml)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy HTML to clipboard:', err)
    }
  }

  const returnVariable = (variable: any, styles: string) => {
    return (
      <>
        {getVariableTypeFieldType(variable.variable_type) === "Imatge" || getVariableTypeFieldType(variable.variable_type) === "URL" ? (
          <Input
            className={styles}
            value={variable.default_value.es || 'Sense valor'}
            readOnly
          />
        ) : (
          <div className={styles}>
            {getVariableTypeFieldType(variable.variable_type)}
            {variable.default_value.es || 'Sense valor'}
          </div>
        )}
      </>
    )
  }

  if (!block) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-7xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            {block.name}
          </DialogTitle>
          <DialogDescription>
            {block.description || 'Detalls del bloc i vista prèvia'}
          </DialogDescription>
        </DialogHeader>

        {/* Información general arriba */}
        <Card className="m-1 p-0">
          <CardHeader className="mt-1 pt-1">
            <CardTitle>Informació General</CardTitle>
          </CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-5 gap-4 pb-3">
            <div>
              <Label className="text-sm font-medium text-muted-foreground">Marca</Label>
              <p className="text-sm font-medium">{block.brand_name}</p>
            </div>
            <div>
              <Label className="text-sm font-medium text-muted-foreground">Estat</Label>
              <div className="flex gap-2 mt-1">
                <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${block.is_active
                  ? "bg-green-100 text-green-800"
                  : "bg-red-100 text-red-800"
                  }`}>{block.is_active ? 'Actiu' : 'Inactiu'}
                </div>
              </div>
            </div>
            <div>
              <Label className="text-sm font-medium text-muted-foreground">Variables</Label>
              <p className="text-sm font-medium">{block.variables?.length || 0}</p>
            </div>
            <div>
              <Label className="text-sm font-medium text-muted-foreground">Creat</Label>
              <p className="text-sm">{block.created_by?.first_name} {block.created_by?.last_name}</p>
              <p className="text-xs text-muted-foreground">{formatDate(block.created_at)}</p>
            </div>
            <div>
              <Label className="text-sm font-medium text-muted-foreground">Actualitzat</Label>
              <p className="text-sm">{block.updated_by?.first_name} {block.updated_by?.last_name}</p>
              <p className="text-xs text-muted-foreground">{formatDate(block.updated_at)}</p>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-7 gap-6">
          {/* Left Panel - Variables */}
          <div className="space-y-6 lg:col-span-3">

            {/* Variables - Matching Edit Block Style */}
            {block.variables && block.variables.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Variable className="h-5 w-5" />
                    Variables del Bloc
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {block.variables.map((variable, index) => (
                      <div key={variable.id} className="relative">
                        {/* Variable Header */}
                        <div className="flex items-center justify-between mb-4 pb-3 border-b">
                          <div className="flex items-center gap-3">
                            <div className="flex items-center justify-center w-6 h-6 bg-blue-100 text-blue-600 rounded-full font-medium">
                              {index + 1}
                            </div>
                            <div>
                              <h4 className="font-semibold text-gray-900 h-6">
                                {variable.name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                              </h4>
                            </div>
                          </div>
                          <div className="flex items-center gap-1">
                            <span className="text-xs text-blue-600 font-medium flex items-center gap-1">
                              {isVariableTypeAI(variable.variable_type) && (
                                <SparklesIcon className="h-3 w-3 text-yellow-500" />
                              )}
                              {getVariableTypeName(variable.variable_type)}
                            </span>
                            {getVariableTypeFieldType(variable.variable_type) && (
                              <span className="text-xs text-gray-400">
                                ({getVariableTypeFieldType(variable.variable_type)})
                              </span>
                            )}
                          </div>
                        </div>

                        {/* Language Values - Read Only */}
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <Globe className="h-3 w-3 text-red-500" />
                              <Label className="text-sm font-medium">
                                Espanyol
                              </Label>
                              <span className="text-xs bg-red-100 text-red-700 px-1.5 py-0.5 rounded">ES</span>
                            </div>
                            {returnVariable(variable, 'p-2 bg-gray-50 border border-red-200 rounded text-sm')}
                          </div>

                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <Globe className="h-3 w-3 text-yellow-600" />
                              <Label className="text-sm font-medium">
                                Català
                              </Label>
                              <span className="text-xs bg-yellow-100 text-yellow-700 px-1.5 py-0.5 rounded">CA</span>
                            </div>
                            {returnVariable(variable, 'p-2 bg-gray-50 border border-yellow-200 rounded text-sm')}
                          </div>

                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <Globe className="h-3 w-3 text-blue-500" />
                              <Label className="text-sm font-medium">
                                Francès
                              </Label>
                              <span className="text-xs bg-blue-100 text-blue-700 px-1.5 py-0.5 rounded">FR</span>
                            </div>
                            {returnVariable(variable, 'p-2 bg-gray-50 border border-blue-200 rounded text-sm')}
                          </div>

                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <Globe className="h-3 w-3 text-green-500" />
                              <Label className="text-sm font-medium">
                                Anglès
                              </Label>
                              <span className="text-xs bg-green-100 text-green-700 px-1.5 py-0.5 rounded">EN</span>
                            </div>
                            {returnVariable(variable, 'p-2 bg-gray-50 border border-green-200 rounded text-sm')}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Right Panel - Preview */}
          <div className="space-y-6 lg:col-span-4 min-w-[600px] sticky top-6 max-h-[calc(100vh-48px)] overflow-y-auto" style={{ zIndex: 10 }}>
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <Eye className="h-5 w-5" />
                    Vista prèvia
                  </CardTitle>
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2">
                      <Label className="text-sm font-medium">Idioma:</Label>
                      <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>
                        <SelectTrigger className="w-32">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="es">
                            <div className="flex items-center gap-2">
                              <Globe className="h-3 w-3 text-red-500" />
                              Espanyol
                            </div>
                          </SelectItem>
                          <SelectItem value="ca">
                            <div className="flex items-center gap-2">
                              <Globe className="h-3 w-3 text-yellow-600" />
                              Català
                            </div>
                          </SelectItem>
                          <SelectItem value="fr">
                            <div className="flex items-center gap-2">
                              <Globe className="h-3 w-3 text-blue-500" />
                              Francès
                            </div>
                          </SelectItem>
                          <SelectItem value="en">
                            <div className="flex items-center gap-2">
                              <Globe className="h-3 w-3 text-green-500" />
                              Anglès
                            </div>
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={copyHtmlToClipboard}
                      disabled={!previewHtml}
                      className="flex items-center gap-2"
                    >
                      {copied ? (
                        <>
                          <Check className="h-4 w-4" />
                          Copiat!
                        </>
                      ) : (
                        <>
                          <Copy className="h-4 w-4" />
                          Copiar HTML
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="border rounded-lg p-6 pb-0 pl-0 pr-0 min-w-[600px] max-w-[640px] shadow-inner">
                  {previewHtml ? (
                    <div dangerouslySetInnerHTML={{ __html: previewHtml }} />
                  ) : (
                    <div className="flex items-center justify-center h-full text-muted-foreground">
                      <div className="text-center">
                        <Eye className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>No hi ha contingut per previsualitzar</p>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* HTML Source */}
            <Card>
              <CardHeader>
                <CardTitle>Codi HTML</CardTitle>
                <CardDescription>
                  Codi font del bloc
                </CardDescription>
              </CardHeader>
              <CardContent>
                <pre className="text-xs bg-muted p-4 rounded-lg overflow-x-auto max-h-60">
                  <code>{block.html_content}</code>
                </pre>
              </CardContent>
            </Card>
          </div>
        </div>

        <div className="flex justify-end pt-4 border-t">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Tancar
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
