"use client"

import React, { use<PERSON>emo, useState, useEffect } from "react"
import { Eye, FileText, Variable, Globe } from "lucide-react"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { useTemplateDetail } from "@/hooks/use-template-detail"
import { useVariableTypes } from "@/hooks/use-variable-types"
import { useLanguages } from "@/hooks/use-languages"
import { Spinner } from '@/components/ui/shadcn-io/spinner';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "../ui/table"
import { formatDate } from "@/lib/utils"
import type { BlockVariable } from "@/types/block"

interface ViewTemplateModalProps {
  templateId: string | null
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function ViewTemplateModal({ templateId, open, onOpenChange }: ViewTemplateModalProps) {
  const { template, loading } = useTemplateDetail(templateId || undefined)
  const { variableTypes, loading: variableTypesLoading } = useVariableTypes()
  const { languages, loading: languagesLoading } = useLanguages()
  const [selectedLanguage, setSelectedLanguage] = useState("es")
  const [previewHtml, setPreviewHtml] = useState("")

  // Helper function to get variable type name
  const getVariableTypeName = (variableTypeId: string) => {
    if (variableTypesLoading) return 'Loading...'
    const variableType = variableTypes.find(type => type.id === variableTypeId)
    return variableType ? variableType.display_name : 'Unknown Type'
  }

  // Collect all unique variables from all blocks in the template
  const allVariables = useMemo(() => {
    if (!template) return []

    const variableMap = new Map<string, BlockVariable>()

    template.template_blocks.forEach(tb => {
      if (tb.block.variables && Array.isArray(tb.block.variables)) {
        tb.block.variables.forEach(variable => {
          // Use variable name as key to avoid duplicates
          if (!variableMap.has(variable.name)) {
            variableMap.set(variable.name, variable)
          }
        })
      }
    })

    return Array.from(variableMap.values()).sort((a, b) => a.name.localeCompare(b.name))
  }, [template])

  // Update preview HTML when template or language changes
  useEffect(() => {
    if (!template) return

    let html = ""
    for (const tb of template.template_blocks.sort((a, b) => a.order_position - b.order_position)) {
      let content = tb.block.html_content || ""

      // Replace variables in HTML with their values in the selected language
      if (tb.block.variables && Array.isArray(tb.block.variables)) {
        tb.block.variables.forEach((variable) => {
          const regex = new RegExp(`\\{\\{\\s*${variable.name}\\s*\\}\\}`, 'g')
          const languageValue = variable.default_value[selectedLanguage as keyof typeof variable.default_value] || ''
          content = content.replace(regex, languageValue)
        })
      }

      html += `\n<!-- Bloc: ${tb.block.name} (#${tb.order_position}) -->\n` + content + "\n"
    }
    setPreviewHtml(html)
  }, [template, selectedLanguage])

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-7xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="pb-0">
          <DialogTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            {template?.name || "Vista de Plantilla"}
          </DialogTitle>
          <DialogDescription>
            {template?.description || "Detalls complets i vista prèvia de la plantilla seleccionada"}
          </DialogDescription>
        </DialogHeader>

        {loading ? (
          <div className="flex flex-col items-center justify-center py-12 space-y-4">
            <Spinner key="infinite" variant="infinite" size={48} />
            <span className="text-muted-foreground">
              Carregant detalls de la plantilla...
            </span>
          </div>
        ) : template ? (
          <div className="space-y-6">
            {/* Información general arriba */}
            <Card className="m-1 p-0">
              <CardHeader className="mt-1 pt-1">
                <CardTitle>Informació General</CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-1 md:grid-cols-5 gap-4 pb-3">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Marca</label>
                  <p className="text-sm font-medium">{template.brand_name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Estat</label>
                  <div className="flex gap-2 mt-1">
                    <Badge variant={template.is_active ? 'default' : 'secondary'}>
                      {template.is_active ? 'Activa' : 'Inactiva'}
                    </Badge>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Total Blocs</label>
                  <p className="text-sm font-medium">{template.total_blocks}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Creat</label>
                  <p className="text-xs text-muted-foreground">{formatDate(template.created_at)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Actualitzat</label>
                  <p className="text-xs text-muted-foreground">{formatDate(template.updated_at)}</p>
                </div>
              </CardContent>
            </Card>

            <div className="grid grid-cols-1 lg:grid-cols-7 gap-6">
              {/* Left Panel - Template Blocks and Variables */}
              <div className="space-y-4 lg:col-span-3">

                {/* Template Blocks */}
                <Card>
                  <CardHeader>
                    <CardTitle>Blocs ({template.total_blocks})</CardTitle>
                    <CardDescription>Estructura i ordre dels blocs de contingut</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="max-h-96 overflow-y-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead className="w-16 text-center font-semibold">#</TableHead>
                            <TableHead className="font-semibold">Nom del Bloc</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {template.template_blocks
                            .sort((a, b) => a.order_position - b.order_position)
                            .map((tb) => (
                              <TableRow key={tb.id} className="hover:bg-muted/50 transition-colors">
                                <TableCell className="text-center font-mono text-sm font-medium text-muted-foreground">
                                  {tb.order_position}
                                </TableCell>
                                <TableCell className="font-medium">
                                  <div className="flex items-center gap-2">
                                    <span className="text-sm truncate max-w-[150px]" title={tb.block.name}>
                                      {tb.block.name}
                                    </span>
                                  </div>
                                </TableCell>
                              </TableRow>
                            ))}
                        </TableBody>
                      </Table>
                    </div>
                  </CardContent>
                </Card>

                {/* Variables Section */}
                {allVariables.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Variable className="h-5 w-5" />
                        Variables ({allVariables.length})
                      </CardTitle>
                      <CardDescription>
                        Variables trobades en els blocs de la plantilla
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="max-h-96 overflow-y-auto space-y-4">
                        {allVariables.map((variable) => (
                          <div key={variable.id} className="border rounded-lg p-4 space-y-3">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <code className="text-sm bg-muted px-2 py-1 rounded font-mono">
                                  {`{{${variable.name}}}`}
                                </code>
                                <Badge variant="outline" className="text-xs">
                                  {getVariableTypeName(variable.variable_type)}
                                </Badge>
                              </div>
                            </div>

                            {/* Language Values */}
                            <div className="grid grid-cols-1 gap-2">
                              <div className="space-y-1">
                                <div className="flex items-center gap-2">
                                  <Globe className="h-3 w-3 text-red-500" />
                                  <Label className="text-xs font-medium">Espanyol</Label>
                                  <span className="text-xs bg-red-100 text-red-700 px-1.5 py-0.5 rounded">ES</span>
                                </div>
                                <p className="text-sm text-muted-foreground bg-muted/50 p-2 rounded border-red-200">
                                  {variable.default_value.es || 'No definit'}
                                </p>
                              </div>

                              <div className="space-y-1">
                                <div className="flex items-center gap-2">
                                  <Globe className="h-3 w-3 text-blue-500" />
                                  <Label className="text-xs font-medium">Català</Label>
                                  <span className="text-xs bg-blue-100 text-blue-700 px-1.5 py-0.5 rounded">CA</span>
                                </div>
                                <p className="text-sm text-muted-foreground bg-muted/50 p-2 rounded border-blue-200">
                                  {variable.default_value.ca || 'No definit'}
                                </p>
                              </div>

                              <div className="space-y-1">
                                <div className="flex items-center gap-2">
                                  <Globe className="h-3 w-3 text-green-500" />
                                  <Label className="text-xs font-medium">Francès</Label>
                                  <span className="text-xs bg-green-100 text-green-700 px-1.5 py-0.5 rounded">FR</span>
                                </div>
                                <p className="text-sm text-muted-foreground bg-muted/50 p-2 rounded border-green-200">
                                  {variable.default_value.fr || 'No definit'}
                                </p>
                              </div>

                              <div className="space-y-1">
                                <div className="flex items-center gap-2">
                                  <Globe className="h-3 w-3 text-purple-500" />
                                  <Label className="text-xs font-medium">Anglès</Label>
                                  <span className="text-xs bg-purple-100 text-purple-700 px-1.5 py-0.5 rounded">EN</span>
                                </div>
                                <p className="text-sm text-muted-foreground bg-muted/50 p-2 rounded border-purple-200">
                                  {variable.default_value.en || 'No definit'}
                                </p>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>

              {/* Right Panel - Preview */}
              <div className="lg:col-span-4 space-y-6">
                <div className="sticky top-6 max-h-[calc(100vh-200px)] overflow-y-auto" style={{ zIndex: 10 }}>
                  <Card>
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div>
                          <CardTitle className="flex items-center gap-2">
                            <Eye className="h-5 w-5" />
                            Vista Prèvia
                          </CardTitle>
                          <CardDescription>
                            Com es veu la plantilla amb els valors de les variables
                          </CardDescription>
                        </div>
                        <div className="flex items-center gap-3">
                          <div className="flex items-center gap-2">
                            <Label className="text-sm font-medium">Idioma:</Label>
                            <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>
                              <SelectTrigger className="w-32">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="es">
                                  <div className="flex items-center gap-2">
                                    <Globe className="h-3 w-3 text-red-500" />
                                    Espanyol
                                  </div>
                                </SelectItem>
                                <SelectItem value="ca">
                                  <div className="flex items-center gap-2">
                                    <Globe className="h-3 w-3 text-yellow-600" />
                                    Català
                                  </div>
                                </SelectItem>
                                <SelectItem value="fr">
                                  <div className="flex items-center gap-2">
                                    <Globe className="h-3 w-3 text-green-500" />
                                    Francès
                                  </div>
                                </SelectItem>
                                <SelectItem value="en">
                                  <div className="flex items-center gap-2">
                                    <Globe className="h-3 w-3 text-blue-500" />
                                    Anglès
                                  </div>
                                </SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="border rounded-lg p-4 bg-white min-w-[320px] max-h-96 overflow-y-auto">
                        {previewHtml ? (
                          <div dangerouslySetInnerHTML={{ __html: previewHtml }} />
                        ) : (
                          <div className="text-sm text-muted-foreground">No hi ha contingut per mostrar.</div>
                        )}
                      </div>
                    </CardContent>
                  </Card>

                  {/* HTML Source */}
                  <Card className="mt-6">
                    <CardHeader>
                      <CardTitle>Codi HTML</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="max-h-60 overflow-y-auto">
                        <pre className="text-xs bg-muted p-4 rounded-lg overflow-x-auto">
                          <code>{previewHtml}</code>
                        </pre>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-12 space-y-4">
            <div className="p-4 bg-red-50 rounded-full">
              <FileText className="h-8 w-8 text-red-500" />
            </div>
            <div className="text-center">
              <h3 className="font-medium">Plantilla no trobada</h3>
              <p className="text-sm text-muted-foreground">No s'han pogut carregar les dades de la plantilla.</p>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}

