"use client"

import * as React from "react"
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"
import { ArrowUpDown, Eye, Pencil, Trash2, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from "lucide-react"

import { Button } from "@/components/ui/button"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Block } from "@/types/block"
import { ViewBlockModal } from "@/components/modals/view-block-modal"
import { EditBlockModal } from "@/components/modals/edit-block-modal"
import { useDeleteBlock } from "@/hooks/use-delete-block"
import { Spinner } from "../ui/shadcn-io/spinner"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "../ui/tooltip"
import { formatDate } from "@/lib/utils"

interface PaginationProps {
  page: number
  pageSize: number
  totalCount: number
  hasNext: boolean
  hasPrevious: boolean
  onPageChange: (page: number) => void
  onPageSizeChange: (pageSize: number) => void
}

interface BlocksTableProps {
  data: Block[]
  loading: boolean
  onRefresh: () => void
  pagination?: PaginationProps
}

export function BlocksTable({ data, loading, onRefresh, pagination }: BlocksTableProps) {
  const [sorting, setSorting] = React.useState<SortingState>([])
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({})
  const [rowSelection, setRowSelection] = React.useState({})

  // Modal states
  const [viewModalOpen, setViewModalOpen] = React.useState(false)
  const [editModalOpen, setEditModalOpen] = React.useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false)
  const [selectedBlock, setSelectedBlock] = React.useState<Block | null>(null)

  // Delete hook
  const { deleteBlock, loading: deleteLoading } = useDeleteBlock()

  // Action handlers
  const handleView = (block: Block) => {
    setSelectedBlock(block)
    setViewModalOpen(true)
  }

  const handleEdit = (block: Block) => {
    setSelectedBlock(block)
    setEditModalOpen(true)
  }

  const handleDelete = (block: Block) => {
    setSelectedBlock(block)
    setDeleteDialogOpen(true)
  }

  const confirmDelete = async () => {
    if (!selectedBlock) return

    try {
      await deleteBlock(selectedBlock.id)
      setDeleteDialogOpen(false)
      setSelectedBlock(null)
      onRefresh()
    } catch (error) {
      console.error('Error deleting block:', error)
    }
  }

  const handleEditSuccess = () => {
    onRefresh()
  }

  const columns: ColumnDef<Block>[] = [
        {
      accessorKey: "brand_name",
      header: "Marca",
      cell: ({ row }) => <div>{row.getValue("brand_name")}</div>,
    },
    {
      accessorKey: "name",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            Nom
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        )
      },
      cell: ({ row }) => {
        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div
                  className="font-medium pl-4 cursor-pointer hover:text-primary hover:underline max-w-[200px] truncate"
                  onClick={() => {
                    setSelectedBlock(row.original)
                    setViewModalOpen(true)
                  }}
                >{row.getValue("name")}</div>
              </TooltipTrigger>
              <TooltipContent className="bg-white text-secondary-foreground border-2">
                {row.getValue("name")}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

        )
      },
    },
    {
      accessorKey: "description",
      header: "Descripció",
      cell: ({ row }) => {
        const description = row.getValue("description") as string
        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="max-w-[200px] truncate hover:underline cursor-help">
                  {description}
                </div>
              </TooltipTrigger>
              <TooltipContent className="bg-white text-secondary-foreground border-2">
                {description}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )
      },
    },
    {
      accessorKey: "is_active",
      header: "Estat",
      cell: ({ row }) => {
        const isActive = row.getValue("is_active") as boolean
        return (
          <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${isActive
            ? "bg-green-100 text-green-800"
            : "bg-red-100 text-red-800"
            }`}>
            {isActive ? "Actiu" : "Inactiu"}
          </div>
        )
      },
    },
    {
      accessorKey: "created_at",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            className="p-0 m-0 hover:bg-none"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            Creat
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        )
      },
      cell: ({ row }) => {
        return <div>{formatDate(row.getValue("created_at") as string)}</div>
      },
    },
    {
      accessorKey: "updated_at",
      header: "Actualitzat",
      cell: ({ row }) => {
        return <div>{formatDate(row.getValue("updated_at") as string)}</div>
      },
    },
    {
      id: "actions",
      enableHiding: false,
      cell: ({ row }) => {
        const block = row.original

        return (
          <>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => handleEdit(block)}
            >
              <Pencil className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => handleDelete(block)}
            >
              <Trash2 className="h-4 w-4 text-red-600" />
            </Button>
          </>
        )
      },
    },
  ]

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    ...(pagination ? {} : { getPaginationRowModel: getPaginationRowModel() }),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    ...(pagination ? {
      manualPagination: true,
      pageCount: Math.ceil(pagination.totalCount / pagination.pageSize),
    } : {}),
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      ...(pagination ? {
        pagination: {
          pageIndex: pagination.page - 1, // TanStack uses 0-based indexing
          pageSize: pagination.pageSize,
        },
      } : {}),
    },
  })

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-center h-64">
          <div className="flex flex-col items-center gap-2 mb-4">
            <div>
              <Spinner key="infinite" variant="infinite" size={64} />
            </div>
            <div className="text-center animate-pulse">
              Carregant blocs...
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="w-full">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No s'han trobat blocs.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination Controls */}
      {pagination && (
        <div className="flex items-center justify-between space-x-2 py-4">
          <div className="flex-1 text-sm text-muted-foreground">
            Mostrant {((pagination.page - 1) * pagination.pageSize) + 1} a{" "}
            {Math.min(pagination.page * pagination.pageSize, pagination.totalCount)} de{" "}
            {pagination.totalCount} entrades
          </div>
          <div className="flex items-center space-x-6 lg:space-x-8">
            <div className="flex items-center space-x-2">
              <p className="text-sm font-medium">Files per pàgina</p>
              <select
                value={pagination.pageSize}
                onChange={(e) => {
                  pagination.onPageSizeChange(Number(e.target.value))
                  pagination.onPageChange(1) // Reset to first page when changing page size
                }}
                className="h-8 w-[70px] rounded border border-input bg-background px-3 py-1 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
              >
                {[10, 20, 30, 40, 50].map((pageSize) => (
                  <option key={pageSize} value={pageSize}>
                    {pageSize}
                  </option>
                ))}
              </select>
            </div>
            <div className="flex w-[100px] items-center justify-center text-sm font-medium">
              Pàgina {pagination.page} de{" "}
              {Math.ceil(pagination.totalCount / pagination.pageSize)}
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                className="hidden h-8 w-8 p-0 lg:flex"
                onClick={() => pagination.onPageChange(1)}
                disabled={!pagination.hasPrevious}
              >
                <span className="sr-only">Anar a la primera pàgina</span>
                <ChevronsLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={() => pagination.onPageChange(pagination.page - 1)}
                disabled={!pagination.hasPrevious}
              >
                <span className="sr-only">Anar a la pàgina anterior</span>
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={() => pagination.onPageChange(pagination.page + 1)}
                disabled={!pagination.hasNext}
              >
                <span className="sr-only">Anar a la pàgina següent</span>
                <ChevronRight className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="hidden h-8 w-8 p-0 lg:flex"
                onClick={() => pagination.onPageChange(Math.ceil(pagination.totalCount / pagination.pageSize))}
                disabled={!pagination.hasNext}
              >
                <span className="sr-only">Anar a l'última pàgina</span>
                <ChevronsRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* View Modal */}
      <ViewBlockModal
        block={selectedBlock}
        open={viewModalOpen}
        onOpenChange={setViewModalOpen}
      />

      {/* Edit Modal */}
      <EditBlockModal
        block={selectedBlock}
        open={editModalOpen}
        onOpenChange={setEditModalOpen}
        onSuccess={handleEditSuccess}
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Estàs segur?</AlertDialogTitle>
            <AlertDialogDescription>
              Aquesta acció no es pot desfer. Això eliminarà permanentment el bloc
              "{selectedBlock?.name}" i el traurà del sistema.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel·lar</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              disabled={deleteLoading}
              className="bg-red-600 hover:bg-red-700"
            >
              {deleteLoading ? 'Eliminant...' : 'Eliminar'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
