"use client"

import * as React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { CheckCircle, XCircle, Eye, RefreshCw } from "lucide-react"

interface AIResponse {
  content: string
  language: string
  blockId?: string | null
  type: 'block' | 'general' | 'header_footer'
  originalPrompt?: string
  blockVariables?: Record<string, string>
}

interface AIConfirmationDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  aiResponse: AIResponse | null
  onApply: () => void
  onDiscard: () => void
  onTryAgain: () => void
  originalBlock?: any // Add original block data for comparison
}

export function AIConfirmationDialog({
  open,
  onOpenChange,
  aiResponse,
  onApply,
  onDiscard,
  onTryAgain,
  originalBlock
}: AIConfirmationDialogProps) {

  const getLanguageDisplay = (langCode: string) => {
    const languageMap: Record<string, { display: string; flag: string }> = {
      'es': { display: 'Espanyol', flag: '🇪🇸' },
      'ca': { display: 'Català', flag: '🏴󠁥󠁳󠁣󠁴󠁿' },
      'fr': { display: 'Francès', flag: '🇫🇷' },
      'en': { display: 'Anglès', flag: '🇬🇧' }
    }
    return languageMap[langCode] || { display: langCode.toUpperCase(), flag: '🌐' }
  }

  const handleApply = () => {
    onApply()
    onOpenChange(false)
  }

  const handleDiscard = () => {
    onDiscard()
    onOpenChange(false)
  }

  const handleTryAgain = () => {
    onTryAgain()
    onOpenChange(false)
  }

  if (!aiResponse) return null

  const { display: languageDisplay, flag: languageFlag } = getLanguageDisplay(aiResponse.language)

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5 text-primary" />
            Vista prèvia del contingut generat per IA
          </DialogTitle>
          <DialogDescription>
            Revisa el contingut generat abans d'aplicar-lo al {aiResponse.type === 'block' ? 'bloc' : 'butlletí'}.
          </DialogDescription>
        </DialogHeader>

        {/* Scrollable Content Area - Full Dialog Scroll */}
        <ScrollArea className="max-h-[calc(90vh-180px)] pr-4">
          <div className="space-y-4">
            {/* Response Metadata */}
            <div className="flex items-center gap-2">
              <Badge variant="secondary" className="flex items-center gap-1">
                <span>{languageFlag}</span>
                <span>{languageDisplay}</span>
              </Badge>
              <Badge variant={aiResponse.type === 'block' ? 'default' : 'outline'}>
                {aiResponse.type === 'block' ? 'Bloc específic' : 'General'}
              </Badge>
            </div>

            {/* Original Prompt (if provided) */}
            {aiResponse.originalPrompt && (
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm">Instruccions originals</CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <p className="text-sm text-muted-foreground italic">
                    "{aiResponse.originalPrompt}"
                  </p>
                </CardContent>
              </Card>
            )}

            {/* Generated Content Preview */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Canvis proposats</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                {aiResponse.blockVariables ? (
                  // Display block variables comparison in a compact way
                  <div className="space-y-3">
                    {Object.entries(aiResponse.blockVariables).map(([variableName, newValue]) => {
                      // Get original value for comparison
                      const originalValue = originalBlock?.variable_values?.find(
                        (v: any) => v.name === variableName
                      )?.value?.[aiResponse.language] || ''

                      return (
                        <div key={variableName} className="border rounded-md p-3">
                          <div className="flex items-center gap-2 mb-3">
                            <Badge variant="outline" className="text-xs">
                              {variableName}
                            </Badge>
                          </div>

                          {/* Comparison View */}
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                            {/* Original Value */}
                            <div className="space-y-1">
                              <div className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                                Actual
                              </div>
                              <div className="text-sm p-2 bg-red-50 border border-red-200 rounded text-red-800 min-h-[60px] break-words">
                                {originalValue || <span className="italic text-red-400">Buit</span>}
                              </div>
                            </div>

                            {/* New Value */}
                            <div className="space-y-1">
                              <div className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                                Nou
                              </div>
                              <div className="text-sm p-2 bg-green-50 border border-green-200 rounded text-green-800 min-h-[60px] break-words">
                                {newValue}
                              </div>
                            </div>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                ) : (
                  // Display HTML content for non-block responses
                  <div className="rounded-md border p-4">
                    <div
                      className="prose prose-sm max-w-none"
                      dangerouslySetInnerHTML={{ __html: aiResponse.content }}
                    />
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Warning Message */}
            <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
              <p className="text-sm text-yellow-800">
                <strong>Atenció:</strong> Aplicar aquests canvis substituirà el contingut actual.
                Assegura't que el contingut generat és adequat abans de continuar.
              </p>
            </div>
          </div>
        </ScrollArea>

        {/* Fixed Footer */}
        <DialogFooter className="flex justify-between">
          <Button
            variant="destructive"
            onClick={handleDiscard}
            className="flex items-center gap-2"
          >
            <XCircle className="mr-2 h-4 w-4" />
            Descartar
          </Button>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={handleTryAgain}
              className="flex items-center gap-2"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Provar de nou
            </Button>
            <Button
              onClick={handleApply}
              className="bg-green-600 hover:bg-green-700 flex items-center gap-2"
            >
              <CheckCircle className="mr-2 h-4 w-4" />
              Aplicar canvis
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
