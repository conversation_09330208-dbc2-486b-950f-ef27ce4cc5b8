// Salesforce folder types
export interface SalesforceFolder {
  id: string
  salesforce_id: number
  name: string
  description: string
  level: number
  full_path: string
  is_root: boolean
  has_children: boolean
  children?: SalesforceFolder[]
}

export interface SalesforceFoldersResponse {
  success: boolean
  format: string
  folders: SalesforceFolder[]
}

export interface CreateSalesforceFolderRequest {
  name: string
  description: string
  parent_id: number
}

export interface CreateSalesforceFolderResponse {
  success: boolean
  folder: SalesforceFolder
}

// Flattened folder for easier selection handling
export interface FlatSalesforceFolder {
  id: string
  salesforce_id: number
  name: string
  description: string
  level: number
  full_path: string
  is_root: boolean
  has_children: boolean
}

// Salesforce sync types
export interface SalesforceSyncRequest {
  newsletter_parent_id: string
}

export interface SalesforceSyncResult {
  newsletter_language_id: string
  language: string
  success: boolean
  asset_id?: number
  asset_name?: string
  customer_key?: string
  asset_data?: any
  error?: string
}

export interface SalesforceSyncResponse {
  detail: string
  newsletter_parent_id: string
  newsletter_parent_name: string
  salesforce_folder: string
  languages_processed: string[]
  total_processed: number
  success_count: number
  error_count: number
  results: SalesforceSyncResult[]
}
