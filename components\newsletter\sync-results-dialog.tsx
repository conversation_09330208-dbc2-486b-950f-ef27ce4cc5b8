"use client"

import { useState } from 'react'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { CheckCircle, XCircle, AlertCircle, ExternalLink } from 'lucide-react'
import { SalesforceSyncResponse, SalesforceSyncResult } from '@/types/salesforce'

interface SyncResultsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  syncResponse: SalesforceSyncResponse | null
}

export function SyncResultsDialog({ open, onOpenChange, syncResponse }: SyncResultsDialogProps) {
  if (!syncResponse) return null

  const getStatusIcon = (success: boolean) => {
    return success ? (
      <CheckCircle className="h-4 w-4 text-green-600" />
    ) : (
      <XCircle className="h-4 w-4 text-red-600" />
    )
  }

  const getStatusBadge = (success: boolean) => {
    return success ? (
      <Badge variant="default" className="bg-green-100 text-green-800 border-green-200">
        Èxit
      </Badge>
    ) : (
      <Badge variant="destructive">
        Error
      </Badge>
    )
  }

  const getLanguageDisplay = (language: string) => {
    const languageMap: Record<string, string> = {
      'es': 'Espanyol',
      'ca': 'Català',
      'fr': 'Francès',
      'en': 'Anglès',
      'unknown': 'Desconegut'
    }
    return languageMap[language] || language
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5" />
            Resultats de la Sincronització
          </DialogTitle>
          <DialogDescription>
            Detalls de la sincronització amb Salesforce per a la newsletter "{syncResponse.newsletter_parent_name}"
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Summary */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold mb-2">Resum</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">Carpeta Salesforce:</span>
                <p className="font-medium">{syncResponse.salesforce_folder}</p>
              </div>
              <div>
                <span className="text-muted-foreground">Total processat:</span>
                <p className="font-medium">{syncResponse.total_processed}</p>
              </div>
              <div>
                <span className="text-muted-foreground">Èxits:</span>
                <p className="font-medium text-green-600">{syncResponse.success_count}</p>
              </div>
              <div>
                <span className="text-muted-foreground">Errors:</span>
                <p className="font-medium text-red-600">{syncResponse.error_count}</p>
              </div>
            </div>
          </div>

          {/* Results by Language */}
          <div>
            <h3 className="font-semibold mb-3">Resultats per Idioma</h3>
            <div className="space-y-3">
              {syncResponse.results.map((result: SalesforceSyncResult, index: number) => (
                <div key={index} className="border rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      {getStatusIcon(result.success)}
                      <span className="font-medium">{getLanguageDisplay(result.language)}</span>
                      {getStatusBadge(result.success)}
                    </div>
                  </div>

                  {result.success ? (
                    <div className="text-sm text-muted-foreground space-y-1">
                      {result.asset_name && (
                        <p><span className="font-medium">Nom:</span> {result.asset_name}</p>
                      )}
                      {result.asset_id && (
                        <p><span className="font-medium">ID Asset:</span> {result.asset_id}</p>
                      )}
                      {result.customer_key && (
                        <p><span className="font-medium">Customer Key:</span> {result.customer_key}</p>
                      )}
                    </div>
                  ) : (
                    <div className="text-sm text-red-600 bg-red-50 p-2 rounded">
                      <span className="font-medium">Error:</span> {result.error}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end pt-4">
            <Button onClick={() => onOpenChange(false)}>
              Tancar
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
