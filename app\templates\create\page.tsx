"use client"

import Re<PERSON>, { use<PERSON><PERSON><PERSON>, use<PERSON>em<PERSON>, useState } from "react"
import { useRouter } from "next/navigation"
import { useSession } from "next-auth/react"
import { NextPage } from 'next'
import { Save, Eye, GripVertical as GripVerticalIcon } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useBrands } from "@/hooks/use-brands"
import { useBlocks } from "@/hooks/use-blocks"
import { useCreateTemplate } from "@/hooks/use-create-template"
import { toast } from "sonner"
import {
  Dnd<PERSON>ontext,
  KeyboardSensor,
  MouseSensor,
  TouchSensor,
  useSensor,
  useSensors,
  type DragEndEvent,
} from "@dnd-kit/core"
import { SortableContext, arrayMove, useSortable, verticalListSortingStrategy } from "@dnd-kit/sortable"
import { CSS } from "@dnd-kit/utilities"

interface Props { }

const Page: NextPage<Props> = ({ }) => {
  const router = useRouter()
  const { status } = useSession()
  const { brands } = useBrands()
  const [brandFilter, setBrandFilter] = useState<string>("")
  const { blocks } = useBlocks({ page: 1, pageSize: 100, brand: brandFilter || undefined })
  const { createTemplate, loading } = useCreateTemplate()

  const [formData, setFormData] = useState({
    name: "",
    brand: "",
    description: "",
    template_blocks: [] as { block_id: string; order_position: number }[],
  })



  // Keep hook filter in sync with selected brand
  useEffect(() => {
    setBrandFilter(formData.brand)
  }, [formData.brand])

  const availableBlocks = useMemo(() => [...blocks].sort((a, b) => a.name.localeCompare(b.name)), [blocks])

  // Build live preview HTML
  const previewHtml = useMemo(() => {
    let html = ""
    for (const tb of formData.template_blocks) {
      const block = availableBlocks.find(b => b.id === tb.block_id)
      if (!block) continue
      let content = block.html_content || ""
      // Keep variables as placeholders - don't replace them with default values
      html += `\n<!-- Bloc: ${block.name} (#${tb.order_position}) -->\n` + content + "\n"
    }
    return html
  }, [formData.template_blocks, availableBlocks])

  // Drag and drop sensors
  const sensors = useSensors(
    useSensor(MouseSensor, { activationConstraint: { distance: 5 } }),
    useSensor(TouchSensor),
    useSensor(KeyboardSensor)
  )

  function handleDragEnd(event: DragEndEvent) {
    const { active, over } = event
    if (!active || !over) return

    setFormData((prev) => {
      const oldIndex = parseInt(String(active.id), 10)
      const newIndex = parseInt(String(over.id), 10)
      if (Number.isNaN(oldIndex) || Number.isNaN(newIndex) || oldIndex === newIndex) return prev
      const moved = arrayMove(prev.template_blocks, oldIndex, newIndex)
      return {
        ...prev,
        template_blocks: moved.map((tb, i) => ({ ...tb, order_position: i + 1 }))
      }
    })
  }

  // Helper component: sortable row for selected blocks table
  function SortableRow({
    id,
    idx,
    name,
    onRemove,
  }: {
    id: number
    idx: number
    name: string
    onRemove: () => void
  }) {
    const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({ id })

    return (
      <TableRow
        ref={setNodeRef}
        data-dragging={isDragging}
        className="relative z-0 data-[dragging=true]:z-10 data-[dragging=true]:opacity-80"
        style={{ transform: CSS.Transform.toString(transform), transition }}
      >
        <TableCell className="w-8">
          <Button {...attributes} {...listeners} variant="ghost" size="icon" className="size-7 text-muted-foreground hover:bg-transparent">
            <GripVerticalIcon className="size-3" />
            <span className="sr-only">Arrossegar per reordenar</span>
          </Button>
        </TableCell>
        <TableCell className="w-10 text-muted-foreground text-xs">{idx + 1}</TableCell>
        <TableCell>
          <div className="font-medium">{name}</div>
        </TableCell>
        <TableCell className="text-right">
          <Button size="sm" variant="destructive" onClick={onRemove}>Treure</Button>
        </TableCell>
      </TableRow>
    )
  }

  const handleAddBlock = (blockId: string) => {
    setFormData((prev) => ({
      ...prev,
      template_blocks: [
        ...prev.template_blocks,
        { block_id: blockId, order_position: prev.template_blocks.length + 1 },
      ],
    }))
  }

  const handleRemoveBlock = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      template_blocks: prev.template_blocks
        .filter((_, i) => i !== index)
        .map((tb, i) => ({ ...tb, order_position: i + 1 })),
    }))
  }



  const validateForm = () => {
    if (!formData.name.trim()) {
      toast.error('El nom de la plantilla és obligatori')
      return false
    }

    if (!formData.brand) {
      toast.error('Si us plau, selecciona una marca')
      return false
    }

    if (formData.template_blocks.length === 0) {
      toast.error('Afegeix almenys un bloc a la plantilla')
      return false
    }

    return true
  }

  const handleSubmit = async () => {
    if (!validateForm()) return

    try {
      await createTemplate({ ...formData, is_active: true })
      toast.success('Plantilla creada amb èxit!')
      router.push('/templates')
    } catch (e) {
      console.error(e)
    }
  }

  if (status === "loading") {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-32">
          <div className="text-muted-foreground">Carregant...</div>
        </div>
      </div>
    )
  }

  if (status === "unauthenticated") {
    router.push('/login')
    return null
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Crear Plantilla</h1>
          <p className="text-muted-foreground">
            Crea una nova plantilla de contingut per a les teves newsletters amb vista prèvia en directe
          </p>
        </div>
        <Button onClick={handleSubmit} disabled={loading}>
          <Save className="mr-2 h-4 w-4" />
          {loading ? 'Creant...' : 'Crear Plantilla'}
        </Button>
      </div>

      {/* Template Details - Single Row */}
      <Card>
        <CardContent className="pt-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name-template">Nom de la Plantilla</Label>
              <Input
                id="name-template"
                placeholder="Introdueix el nom de la plantilla"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value.substring(0, 70) }))}
                maxLength={70}
              />
              <p className="text-xs text-muted-foreground">{formData.name.length}/70 caràcters</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="brand">Marca</Label>
              <Select value={formData.brand} onValueChange={(value) => { setFormData(prev => ({ ...prev, brand: value, template_blocks: [] })); setBrandFilter(value) }}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecciona una marca" />
                </SelectTrigger>
                <SelectContent>
                  {brands.map((brand) => (
                    <SelectItem key={brand.id} value={brand.id}>
                      {brand.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Descripció</Label>
              <Input
                id="description"
                placeholder="Introdueix la descripció de la plantilla (opcional)"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value.substring(0, 150) }))}
                maxLength={150}
              />
              <p className="text-xs text-muted-foreground">{formData.description.length}/150 caràcters</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Blocks Management and Preview - Side by Side */}
      <div className="grid grid-cols-1 lg:grid-cols-5 gap-6">
        {/* Left Panel - Blocks Management */}
        <div className="space-y-6 lg:col-span-2">

          {/* Blocks Management */}
          <Card>
            <CardHeader>
              <CardTitle>Blocs Disponibles</CardTitle>
              <CardDescription>
                Afegeix blocs de contingut a la teva plantilla
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Disable until brand selected */}
              {!formData.brand && (
                <div className="p-4 text-sm text-muted-foreground border rounded">
                  Selecciona una marca per veure i afegir blocs.
                </div>
              )}

              {formData.brand && (
                <div className="overflow-hidden rounded-lg border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[50%]">Nom del bloc</TableHead>
                        <TableHead>Marca</TableHead>
                        <TableHead className="text-right"></TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {availableBlocks.length ? (
                        availableBlocks.map((b) => (
                          <TableRow key={b.id}>
                            <TableCell className="font-medium">{b.name}</TableCell>
                            <TableCell>{b.brand_name}</TableCell>
                            <TableCell className="text-right">
                              <Button size="sm" variant="outline" onClick={() => handleAddBlock(b.id)}>
                                Afegir
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={3} className="text-center text-sm text-muted-foreground h-16">
                            No hi ha blocs disponibles per a la marca seleccionada.
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              )}

              {/* Selected blocks as sortable table */}
              {formData.template_blocks.length > 0 && (
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Blocs seleccionats:</Label>
                  <div className="overflow-hidden rounded-lg border">
                    <DndContext sensors={sensors} onDragEnd={handleDragEnd}>
                      <Table>
                        <TableHeader className="sticky top-0 z-10 bg-muted">
                          <TableRow>
                            <TableHead className="w-8"></TableHead>
                            <TableHead className="w-10">#</TableHead>
                            <TableHead>Bloc</TableHead>
                            <TableHead className="text-right"></TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {/* Provide ids as indexes for SortableContext */}
                          <SortableContext items={formData.template_blocks.map((_, idx) => idx)} strategy={verticalListSortingStrategy}>
                            {formData.template_blocks.map((tb, idx) => {
                              const block = availableBlocks.find((b) => b.id === tb.block_id)
                              return (
                                <SortableRow
                                  key={`${tb.block_id}-${idx}`}
                                  id={idx}
                                  idx={idx}
                                  name={block?.name || tb.block_id}
                                  onRemove={() => handleRemoveBlock(idx)}
                                />
                              )
                            })}
                          </SortableContext>
                        </TableBody>
                      </Table>
                    </DndContext>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Right Panel - Preview */}
        <div
          className="space-y-6 lg:col-span-3 sticky top-6 overflow-y-auto"
          style={{ zIndex: 10 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5" />
                Vista Prèvia en Directe
              </CardTitle>
              <CardDescription>
                Veu com es veurà la teva plantilla amb els blocs seleccionats
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="border rounded-lg p-6 w-full shadow-inner">
                {previewHtml ? (
                  <div dangerouslySetInnerHTML={{ __html: previewHtml }} />
                ) : (
                  <div className="flex items-center justify-center h-full text-muted-foreground">
                    <div className="text-center">
                      <Eye className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>Selecciona una marca i afegeix blocs per veure la vista prèvia</p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

export default Page

