import { useState } from 'react'
import { useSession } from 'next-auth/react'
import { toast } from 'sonner'
import { extractApiErrorMessage } from '@/lib/api-error-utils'
import { DEFAULT_API_URL } from '@/constants/constants'

interface UseDeleteBlockReturn {
  deleteBlock: (blockId: string) => Promise<void>
  loading: boolean
  error: string | null
}

export function useDeleteBlock(): UseDeleteBlockReturn {
  const { data: session } = useSession()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const deleteBlock = async (blockId: string) => {
    if (!session?.djangoAccessToken) {
      throw new Error('Authentication required. Please log in again.')
    }

    setLoading(true)
    setError(null)

    try {
      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || DEFAULT_API_URL
      
      const response = await fetch(`${backendUrl}/blocks/delete-block/${blockId}/`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.djangoAccessToken}`
        }
      })

      if (!response.ok) {
        const errorMessage = await extractApiErrorMessage(response, 'Failed to delete block')
        throw new Error(errorMessage)
      }

      toast.success('Bloc eliminat correctament!')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'No s\'ha pogut eliminar el bloc'
      setError(errorMessage)
      toast.error(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }

  return {
    deleteBlock,
    loading,
    error
  }
}
