/**
 * Permission Button Component
 * A button that is disabled or hidden based on user permissions
 */

import React from 'react'
import { Button, ButtonProps } from '@/components/ui/button'
import { useUserPermissions, PermissionCodename } from '@/hooks/use-user-permissions'

interface PermissionButtonProps extends ButtonProps {
  /** Permission codename required to enable the button */
  permission: PermissionCodename | string
  /** Whether to hide the button when permission is denied (default: false - shows disabled) */
  hideWhenDenied?: boolean
  /** Custom tooltip text when button is disabled due to permissions */
  disabledTooltip?: string
}

export function PermissionButton({ 
  permission, 
  hideWhenDenied = false,
  disabledTooltip = "You don't have permission to perform this action",
  disabled,
  children,
  ...buttonProps 
}: PermissionButtonProps) {
  const { hasPermission, loading } = useUserPermissions()
  
  const hasAccess = hasPermission(permission)
  const shouldDisable = loading || !hasAccess || disabled
  
  if (hideWhenDenied && !loading && !hasAccess) {
    return null
  }
  
  return (
    <Button
      {...buttonProps}
      disabled={shouldDisable}
      title={!hasAccess && !loading ? disabledTooltip : buttonProps.title}
    >
      {children}
    </Button>
  )
}