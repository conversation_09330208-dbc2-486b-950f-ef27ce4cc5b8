"use client"

import React, { useState, useEffect } from "react"
import { Save, Eye, Variable, Globe, Tag, Copy, Check, Pencil, SparklesIcon } from "lucide-react"

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { HtmlEditor } from "@/components/ui/html-editor"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { toast } from "sonner"
import { HeaderFooter, HeaderFooterVariable } from "@/types/block"
import { useUpdateHeaderFooter } from "@/hooks/use-update-header-footer"
import { useBrands } from "@/hooks/use-brands"
import { useVariableTypes } from "@/hooks/use-variable-types"
import { UpdateConfirmationDialog } from "@/components/ui/update-confirmation-dialog"
import { hasChanges, extractHeaderFooterData } from "@/lib/change-detection"

interface HeaderFooterFormData {
  name: string
  brand: string
  element_type: 'header' | 'footer'
  html_content: string
  variables: HeaderFooterVariable[]
  is_active: boolean
}

interface EditHeaderFooterModalProps {
  headerFooter: HeaderFooter | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: () => void
}

export function EditHeaderFooterModal({
  headerFooter,
  open,
  onOpenChange,
  onSuccess,
}: EditHeaderFooterModalProps) {
  const { updateHeaderFooter, loading: updateLoading } = useUpdateHeaderFooter()
  const { brands } = useBrands()
  const { variableTypes } = useVariableTypes()

  const [formData, setFormData] = useState<HeaderFooterFormData>({
    name: "",
    brand: "",
    element_type: "header",
    html_content: "",
    variables: [],
    is_active: true,
  })

  const [previewHtml, setPreviewHtml] = useState("")
  const [selectedLanguage, setSelectedLanguage] = useState("es")
  const [copied, setCopied] = useState(false)
  const [originalData, setOriginalData] = useState<any>(null)
  const [showConfirmDialog, setShowConfirmDialog] = useState(false)
  const [showExitConfirmDialog, setShowExitConfirmDialog] = useState(false)

  // Reset form when headerFooter changes
  useEffect(() => {
    function blockVarToVariable(blockVar: any): HeaderFooterVariable {
      return {
        id: blockVar.id,
        header_footer: blockVar.header_footer || headerFooter?.id || '',
        header_footer_name: blockVar.header_footer_name || headerFooter?.name || '',
        variable_type_id: blockVar.variable_type_id || blockVar.variable_type,
        name: blockVar.name,
        default_value: blockVar.default_value,
        variable_type: blockVar.variable_type_id || blockVar.variable_type
      }
    }

    if (headerFooter) {
      const headerFooterData = {
        name: headerFooter.name,
        brand: headerFooter.brand,
        element_type: headerFooter.element_type as "header" | "footer",
        html_content: headerFooter.html_content,
        variables: headerFooter.variables.map(blockVarToVariable),
        is_active: headerFooter.is_active,
      }

      setFormData(headerFooterData)
      setOriginalData(extractHeaderFooterData(headerFooter))
    }
  }, [headerFooter])

  // Extract variables from HTML content (auto-detection)
  useEffect(() => {
    const variableRegex = /\{\{\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*\}\}/g
    const foundVariables = new Set<string>()
    let match

    while ((match = variableRegex.exec(formData.html_content)) !== null) {
      foundVariables.add(match[1])
    }

    // Update variables list
    const newVariables: HeaderFooterVariable[] = Array.from(foundVariables).map(varName => {
      const existing = formData.variables.find(v => v.name === varName)
      return existing || {
        id: '',
        header_footer: headerFooter?.id || '',
        header_footer_name: headerFooter?.name || '',
        variable_type_id: variableTypes.length > 0 ? variableTypes[0].id : "",
        name: varName,
        default_value: { es: '', ca: '', fr: '', en: '' },
        variable_type: variableTypes.length > 0 ? variableTypes[0].id : ""
      }
    })

    setFormData(prev => ({ ...prev, variables: newVariables }))
  }, [formData.html_content, variableTypes])

  // Update preview HTML when content, variables, or language change
  useEffect(() => {
    let html = formData.html_content

    // Replace variables in HTML with their values in the selected language
    formData.variables.forEach((variable) => {
      const regex = new RegExp(`\\{\\{\\s*${variable.name}\\s*\\}\\}`, 'g')
      const languageValue = variable.default_value[selectedLanguage as keyof typeof variable.default_value] || ''
      html = html.replace(regex, languageValue)
    })

    setPreviewHtml(html)
  }, [formData.html_content, formData.variables, selectedLanguage])

  // Copy HTML to clipboard
  const copyHtmlToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(previewHtml)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy HTML to clipboard:', err)
    }
  }

  // Helper functions for variables
  const getVariableTypeName = (variableTypeId: string) => {
    const variableType = variableTypes.find(type => type.id === variableTypeId)
    return variableType ? variableType.display_name : 'Unknown Type'
  }

  const updateVariable = (index: number, field: keyof HeaderFooterVariable, value: any) => {
    setFormData(prev => ({
      ...prev,
      variables: prev.variables.map((variable, i) =>
        i === index ? { ...variable, [field]: value } : variable
      )
    }))
  }

  const updateVariableLanguage = (index: number, language: 'es' | 'ca' | 'fr' | 'en', value: string) => {
    setFormData(prev => ({
      ...prev,
      variables: prev.variables.map((variable, i) =>
        i === index
          ? {
            ...variable,
            default_value: {
              ...variable.default_value,
              [language]: value
            }
          }
          : variable
      )
    }))
  }

  const handleSubmit = async () => {
    if (!headerFooter) return

    if (!formData.name.trim()) {
      toast.error('El nom de la capçalera/peu és obligatori')
      return
    }

    if (!formData.brand) {
      toast.error('Si us plau, selecciona una marca')
      return
    }



    if (!formData.html_content.trim()) {
      toast.error('El contingut HTML és obligatori')
      return
    }

    // Check if there are any changes
    if (!hasChanges(originalData, formData)) {
      toast.info('No s\'han detectat canvis')
      return
    }

    // Show confirmation dialog
    setShowConfirmDialog(true)
  }

  const handleConfirmUpdate = async () => {
    if (!headerFooter) return

    try {
      await updateHeaderFooter({
        id: headerFooter.id,
        ...formData,
      })
      toast.success('Capçalera/Peu actualitzat correctament!')
      onOpenChange(false)
      onSuccess?.()
      setShowConfirmDialog(false)
    } catch (error) {
      console.error('Failed to update header/footer:', error)
      setShowConfirmDialog(false)
    }
  }

  // Handle dialog close with unsaved changes check
  const handleCloseAttempt = () => {
    if (originalData && hasChanges(originalData, formData)) {
      setShowExitConfirmDialog(true)
    } else {
      onOpenChange(false)
    }
  }

  // Confirm exit without saving
  const handleConfirmExit = () => {
    setShowExitConfirmDialog(false)
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={(open) => !open && handleCloseAttempt()}>
      <DialogContent className="max-w-7xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Pencil className="h-5 w-5" />
            Editar Capçalera/Peu: {headerFooter?.name}
          </DialogTitle>
          <DialogDescription>
            Actualitza la informació i el contingut de la capçalera/peu
          </DialogDescription>
        </DialogHeader>

        {/* Información general arriba */}
        <Card className="m-1 p-0">
        <CardHeader className="mt-1 pt-1">
            <CardTitle>Informació General</CardTitle>
          </CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-4 gap-4 pb-3">
            <div className="space-y-2">
              <Label htmlFor="name-header">Nom *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value.substring(0, 70) }))}
                placeholder="Introdueix el nom de la capçalera/peu"
                maxLength={70}
              />
              <p className="text-xs text-muted-foreground">{formData.name.length}/70 caràcters</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="element_type">Tipus *</Label>
              <Select
                disabled={true}
                value={formData.element_type}
                onValueChange={(value: 'header' | 'footer') => setFormData(prev => ({ ...prev, element_type: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecciona el tipus" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="header">Capçalera</SelectItem>
                  <SelectItem value="footer">Peu</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="brand">Marca *</Label>
              <Select
                disabled={true}
                value={formData.brand}
                onValueChange={(value) => setFormData(prev => ({ ...prev, brand: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecciona una marca" />
                </SelectTrigger>
                <SelectContent>
                  {brands.map((brand) => (
                    <SelectItem key={brand.id} value={brand.id}>
                      {brand.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Configuració</Label>
              <div className="flex items-center space-x-2 mt-2">
                <Switch
                  checked={formData.is_active}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: checked }))}
                />
                <Label className="text-sm">Actiu</Label>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-7 gap-6">
          {/* Left Panel - Form */}
          <div className="space-y-6 lg:col-span-3">

            <Card>
              <CardHeader>
                <CardTitle>Contingut HTML</CardTitle>
                <CardDescription>
                  Escriu el teu contingut HTML. Utilitza {`{{ nomVariable }}`} per a variables.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <HtmlEditor
                  placeholder="Introdueix el teu contingut HTML aquí..."
                  value={formData.html_content}
                  onChange={(value) => setFormData(prev => ({ ...prev, html_content: value }))}
                  rows={20}
                />
              </CardContent>
            </Card>

            {/* Variables Section */}
            {/* Variables */}
            {formData.variables.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Variable className="h-5 w-5" />
                    Variables de la Capçalera/Peu
                  </CardTitle>
                  <CardDescription>
                    Configura els valors per defecte per a les variables trobades al teu HTML
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {formData.variables.map((variable, index) => (
                      <div key={index} className="relative">
                        {/* Variable Header */}
                        <div className="flex items-center justify-between mb-4 pb-3 border-b">
                          <div className="flex items-center gap-3">
                            <div className="flex items-center justify-center w-6 h-6 bg-blue-100 text-blue-600 rounded-full text-xs font-medium">
                              {index + 1}
                            </div>
                            <div>
                              <h6 className="font-semibold text-gray-900 h-6">
                                {variable.name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                              </h6>
                              <p className="text-xs text-gray-500 font-mono">
                                {`{{ ${variable.name} }}`}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Select
                              value={variable.variable_type_id}
                              onValueChange={(value) => updateVariable(index, 'variable_type_id', value)}
                            >
                              <SelectTrigger className="w-40">
                                <SelectValue placeholder="Tipus" />
                              </SelectTrigger>
                              <SelectContent>
                                {variableTypes.map((type) => (
                                  <SelectItem key={type.id} value={type.id}>
                                  <div className="flex flex-col">
                                    <span className="font-medium text-xs text-left">{type.display_name}</span>
                                    <span className="text-xs text-muted-foreground text-left flex items-center gap-1">
                                    {type.ai_generated && <SparklesIcon className="h-3 w-3 text-yellow-500" />}
                                    {type.field_type_display}
                                    </span>
                                  </div>
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        </div>

                        {/* Language Inputs */}
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <Globe className="h-3 w-3 text-red-500" />
                              <Label htmlFor={`var-${variable.name}-es`} className="text-sm font-medium">
                                Espanyol
                              </Label>
                              <span className="text-xs bg-red-100 text-red-700 px-1.5 py-0.5 rounded">ES</span>
                            </div>
                            <Input
                              id={`var-${variable.name}-es`}
                              placeholder="Valor en espanyol"
                              value={variable.default_value.es}
                              onChange={(e) => updateVariableLanguage(index, 'es', e.target.value)}
                              className="border-red-200 focus:border-red-400"
                            />
                          </div>

                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <Globe className="h-3 w-3 text-yellow-600" />
                              <Label htmlFor={`var-${variable.name}-ca`} className="text-sm font-medium">
                                Català
                              </Label>
                              <span className="text-xs bg-yellow-100 text-yellow-700 px-1.5 py-0.5 rounded">CA</span>
                            </div>
                            <Input
                              id={`var-${variable.name}-ca`}
                              placeholder="Valor en català"
                              value={variable.default_value.ca}
                              onChange={(e) => updateVariableLanguage(index, 'ca', e.target.value)}
                              className="border-yellow-200 focus:border-yellow-400"
                            />
                          </div>

                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <Globe className="h-3 w-3 text-blue-500" />
                              <Label htmlFor={`var-${variable.name}-fr`} className="text-sm font-medium">
                                Francès
                              </Label>
                              <span className="text-xs bg-blue-100 text-blue-700 px-1.5 py-0.5 rounded">FR</span>
                            </div>
                            <Input
                              id={`var-${variable.name}-fr`}
                              placeholder="Valor en francès"
                              value={variable.default_value.fr}
                              onChange={(e) => updateVariableLanguage(index, 'fr', e.target.value)}
                              className="border-blue-200 focus:border-blue-400"
                            />
                          </div>

                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <Globe className="h-3 w-3 text-green-500" />
                              <Label htmlFor={`var-${variable.name}-en`} className="text-sm font-medium">
                                Anglès
                              </Label>
                              <span className="text-xs bg-green-100 text-green-700 px-1.5 py-0.5 rounded">EN</span>
                            </div>
                            <Input
                              id={`var-${variable.name}-en`}
                              placeholder="Valor en anglès"
                              value={variable.default_value.en}
                              onChange={(e) => updateVariableLanguage(index, 'en', e.target.value)}
                              className="border-green-200 focus:border-green-400"
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Right Panel - Preview */}
          <div
            className="space-y-6 lg:col-span-4 min-w-[300px] sticky top-6 max-h-[calc(100vh-48px)] overflow-y-auto"
            style={{ zIndex: 10 }}
          >
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Eye className="h-5 w-5" />
                      Vista prèvia
                    </CardTitle>
                    <CardDescription>
                      Veu com es veurà la teva capçalera/peu
                    </CardDescription>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2">
                      <Label className="text-sm font-medium">Idioma:</Label>
                      <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>
                        <SelectTrigger className="w-32">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="es">
                            <div className="flex items-center gap-2">
                              <Globe className="h-3 w-3 text-red-500" />
                              Espanyol
                            </div>
                          </SelectItem>
                          <SelectItem value="ca">
                            <div className="flex items-center gap-2">
                              <Globe className="h-3 w-3 text-yellow-600" />
                              Català
                            </div>
                          </SelectItem>
                          <SelectItem value="fr">
                            <div className="flex items-center gap-2">
                              <Globe className="h-3 w-3 text-blue-500" />
                              Francès
                            </div>
                          </SelectItem>
                          <SelectItem value="en">
                            <div className="flex items-center gap-2">
                              <Globe className="h-3 w-3 text-green-500" />
                              Anglès
                            </div>
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={copyHtmlToClipboard}
                      disabled={!previewHtml}
                      className="flex items-center gap-2"
                    >
                      {copied ? (
                        <>
                          <Check className="h-4 w-4" />
                          Copiat!
                        </>
                      ) : (
                        <>
                          <Copy className="h-4 w-4" />
                          Copiar HTML
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="border rounded-lg p-6 pb-0 pl-0 pr-0 min-w-[600px] max-w-[640px] shadow-inner">
                  {previewHtml ? (
                    <div dangerouslySetInnerHTML={{ __html: previewHtml }} />
                  ) : (
                    <div className="flex items-center justify-center h-full text-muted-foreground">
                      <div className="text-center">
                        <Eye className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>No hi ha contingut per previsualitzar</p>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        <div className="flex justify-end gap-2 pt-4 border-t">
          <Button variant="outline" onClick={handleCloseAttempt}>
            Cancel·lar
          </Button>
          <Button onClick={handleSubmit} disabled={updateLoading}>
            {updateLoading ? 'Actualitzant...' : 'Actualitzar Capçalera/Peu'}
          </Button>
        </div>

        <UpdateConfirmationDialog
          open={showConfirmDialog}
          onOpenChange={setShowConfirmDialog}
          onConfirm={handleConfirmUpdate}
          title="Confirmar Actualització de Capçalera/Peu"
          description="Estàs segur que vols actualitzar aquesta capçalera/peu? Aquesta acció desarà tots els teus canvis."
          confirmText="Actualitzar Capçalera/Peu"
          cancelText="Cancel·lar"
        />

        <UpdateConfirmationDialog
          open={showExitConfirmDialog}
          onOpenChange={setShowExitConfirmDialog}
          onConfirm={handleConfirmExit}
          title="Descartar Canvis"
          description="Tens canvis sense desar. Estàs segur que vols sortir sense desar? Es perdran tots els canvis realitzats."
          confirmText="Sortir sense Desar"
          cancelText="Continuar Editant"
        />
      </DialogContent>
    </Dialog>
  )
}
