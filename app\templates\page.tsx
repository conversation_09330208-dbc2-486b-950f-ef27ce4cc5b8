"use client"

import React, { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { useSession } from "next-auth/react"
import { NextPage } from 'next'
import { Plus, Search } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { TemplatesTable } from "@/components/table/templates-table"
import { useTemplates } from "@/hooks/use-templates"
import { useBrands } from "@/hooks/use-brands"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DEFAULT_PAGE_SIZE } from "@/constants/constants"

interface Props {}

const Page: NextPage<Props> = ({}) => {
  const router = useRouter()
  const { status } = useSession()
  const { brands } = useBrands()
  const [templatesSearchQuery, setTemplatesSearchQuery] = useState("")
  const [debouncedTemplatesSearch, setDebouncedTemplatesSearch] = useState("")
  const [selectedBrand, setSelectedBrand] = useState<string>("")
  // Pagination state for templates
  const [templatesPage, setTemplatesPage] = useState(1)
  const [templatesPageSize, setTemplatesPageSize] = useState(DEFAULT_PAGE_SIZE)

  // Debounce templates search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedTemplatesSearch(templatesSearchQuery)
    }, 300)

    return () => clearTimeout(timer)
  }, [templatesSearchQuery])

  // Reset templates page when search or brand changes
  useEffect(() => {
    setTemplatesPage(1)
  }, [debouncedTemplatesSearch, selectedBrand])

  // Fetch templates with search and brand filters
  const {
    templates,
    loading: templatesLoading,
    error: templatesError,
    refetch: refetchTemplates,
    totalCount: templatesTotalCount,
    hasNext: templatesHasNext,
    hasPrevious: templatesHasPrevious
  } = useTemplates({
    search: debouncedTemplatesSearch || undefined,
    brand: selectedBrand && selectedBrand !== "all" ? selectedBrand : undefined,
    page: templatesPage,
    pageSize: templatesPageSize
  })

  const handleCreateTemplate = () => {
    router.push('/templates/create')
  }

  // Show loading while session is being fetched
  if (status === "loading") {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-32">
          <div className="text-muted-foreground">Carregant...</div>
        </div>
      </div>
    )
  }

  // Redirect to login if not authenticated
  if (status === "unauthenticated") {
    router.push('/login')
    return null
  }

  return (
      <div className="p-6 space-y-8">
        {/* Templates Section */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Plantilles</h1>
              <p className="text-muted-foreground">
                Gestiona les plantilles per les Newsletters. Aquestes estan formades per blocs, i es seleccionen en el moment de crear una Newsletter.
              </p>
            </div>
            <Button onClick={handleCreateTemplate}>
              <Plus className="mr-2 h-4 w-4" />
              Crear Plantilla
            </Button>
          </div>

        <div className="flex items-center space-x-4">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder="Cercar plantilles..."
              value={templatesSearchQuery}
              onChange={(e) => setTemplatesSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="space-y-2">
            <Select value={selectedBrand} onValueChange={setSelectedBrand}>
              <SelectTrigger className="w-[200px]">
                <SelectValue placeholder="Totes les marques" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Totes les marques</SelectItem>
                {brands.map((brand) => (
                  <SelectItem key={brand.id} value={brand.id}>
                    {brand.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {templatesError && (
          <div className="p-4 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
            Error: {templatesError}
          </div>
        )}

        <TemplatesTable
          data={templates}
          loading={templatesLoading}
          onRefresh={refetchTemplates}
          pagination={{
            page: templatesPage,
            pageSize: templatesPageSize,
            totalCount: templatesTotalCount,
            hasNext: templatesHasNext,
            hasPrevious: templatesHasPrevious,
            onPageChange: setTemplatesPage,
            onPageSizeChange: setTemplatesPageSize
          }}
        />
      </div>
      </div>
  )
}

export default Page