"use client"

import * as React from "react"
import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Bot, Sparkles, Globe, FileText } from "lucide-react"
import { NewsletterBlock, NewsletterHeaderFooter } from "@/types/newsletter"
import { Spinner } from "../ui/shadcn-io/spinner"
import { useLanguageContext } from "@/contexts/language-context"
import { useAIApi } from "@/hooks/use-ai-api"
import { ScrollArea } from "../ui/scroll-area"
import { Badge } from "../ui/badge"
import { useUserPermissions, PERMISSION_CODENAMES } from "@/hooks/use-user-permissions"

interface AIDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  block?: NewsletterBlock | null
  headerFooter?: NewsletterHeaderFooter | null
  newsletterId?: string
  onAIResponse: (response: any, language: string) => void
  debugInfo?: Record<string, any>
}

export function AIDialog({ open, onOpenChange, block, headerFooter, newsletterId, onAIResponse, debugInfo }: AIDialogProps) {
  const { selectedLanguage, languages } = useLanguageContext()
  const { generateContent, generateBlockContent, loading: apiLoading, error: apiError } = useAIApi()
  const { hasPermission } = useUserPermissions()
  const [prompt, setPrompt] = useState("")
  const [selectedAILanguage, setSelectedAILanguage] = useState(selectedLanguage)

  // Check if user has permission to create/edit newsletters
  const canEditNewsletters = hasPermission(PERMISSION_CODENAMES.CREATE_EDIT_NEWSLETTERS)

  // Update AI language when context language changes
  React.useEffect(() => {
    setSelectedAILanguage(selectedLanguage)
  }, [selectedLanguage])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (apiLoading) return

    try {
      // Use the new generateBlockContent API for blocks
      if (block) {
        // Prepare variables from the block for the API call
        const variables: Record<string, string> = {}

        // Extract current variable values for the selected language
        block.variable_values.forEach(variable => {
          const value = variable.value[selectedAILanguage as keyof typeof variable.value]
          variables[variable.name] = value || ''
        })

        console.log('Generating content for block:', block.name, 'with variables:', variables)

        const blockContentResponse = await generateBlockContent({
          language: selectedAILanguage,
          prompt: prompt.trim() || "Genera contingut per al bloc",
          variables
        })

        console.log('Received block content response:', blockContentResponse)

        // Transform the response to match the expected format
        const response = {
          content: JSON.stringify(blockContentResponse),
          language: selectedAILanguage,
          blockId: block.id,
          type: 'block' as const,
          originalPrompt: prompt.trim(),
          blockVariables: blockContentResponse // Add the generated variables
        }

        onAIResponse(response, selectedAILanguage)
      } else {
        // Use the original generateContent for header/footer and general content
        const contextType = headerFooter ? 'header_footer' : 'general'

        const response = await generateContent({
          prompt: prompt.trim() || undefined,
          language: selectedAILanguage,
          context: {
            type: contextType,
            blockId: undefined,
            newsletterId,
            block: undefined,
            headerFooter: headerFooter || undefined,
            debugInfo
          }
        })

        onAIResponse(response, selectedAILanguage)
      }

      onOpenChange(false)
      setPrompt("")
    } catch (error) {
      console.error('Error calling AI API:', error)
      // Error is already handled by the useAIApi hook
    }
  }

  const handleCancel = () => {
    onOpenChange(false)
    setPrompt("")
  }

  const getLanguageDisplay = (langCode: string) => {
    const languageMap: Record<string, { display: string; color: string }> = {
      'es': { display: 'Espanyol', color: 'text-red-500' },
      'ca': { display: 'Català', color: 'text-yellow-500' },
      'fr': { display: 'Francès', color: 'text-blue-500' },
      'en': { display: 'Anglès', color: 'text-green-500' }
    }
    return languageMap[langCode] || { display: langCode.toUpperCase(), color: 'text-gray-500' }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Bot className="h-5 w-5 text-primary" />
              Assistent d'IA
              {block && (
                <span className="text-sm font-normal text-muted-foreground">
                  - {block.name}
                </span>
              )}
          </DialogTitle>
          <DialogDescription>
            {block
              ? `Genera contingut per al bloc "${block.name}" utilitzant intel·ligència artificial.`
              : headerFooter
              ? `Genera contingut per a "${headerFooter.name}" utilitzant intel·ligència artificial.`
              : "Genera contingut per al butlletí utilitzant intel·ligència artificial."
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Language Selector */}
          <div className="space-y-2">
            <Label htmlFor="ai-language">Idioma de generació</Label>
            <Select value={selectedAILanguage} onValueChange={setSelectedAILanguage}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {['ca', 'es', 'fr', 'en'].map((langCode) => {
                  const { display, color } = getLanguageDisplay(langCode)
                  return (
                    <SelectItem key={langCode} value={langCode}>
                      <div className="flex items-center gap-2">
                        <Globe className={`h-3 w-3 ${color}`} />
                        <span>{display}</span>
                      </div>
                    </SelectItem>
                  )
                })}
              </SelectContent>
            </Select>
          </div>

          {/* Variables Preview */}
          {debugInfo?.variables && Array.isArray(debugInfo.variables) && debugInfo.variables.length > 0 && (
            <div className="space-y-2">
              <Label className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Variables ({debugInfo.variables.length})
              </Label>
              <ScrollArea className="h-32 w-full border rounded-lg p-3">
                <div className="space-y-2">
                  {debugInfo.variables.map((variable: any, index: number) => (
                    <div key={variable.name || index} className="flex items-start gap-2 p-2 bg-muted rounded text-sm">
                      <Badge variant="outline" className="text-xs shrink-0">
                        {variable.name}
                      </Badge>
                      <div className="flex-1 text-muted-foreground">
                        <div className="text-xs">
                          <span className="font-medium">Tipus:</span> {variable.type}
                        </div>
                        <div className="text-xs">
                          <span className="font-medium">Té valors:</span> {variable.hasValues ? 'Sí' : 'No'}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
              <p className="text-xs text-muted-foreground">
                Aquestes són les variables disponibles per a la generació de contingut.
              </p>
            </div>
          )}

          {/* Prompt Input */}
          <div className="space-y-2">
            <Label htmlFor="ai-prompt">
              Instruccions per a l'IA <span className="text-muted-foreground">(opcional)</span>
            </Label>
            <Textarea
              id="ai-prompt"
              placeholder={block
                ? `Descriu com vols que l'IA modifiqui el bloc "${block.name}"...`
                : headerFooter
                ? `Descriu com vols que l'IA modifiqui "${headerFooter.name}"...`
                : "Descriu com vols que l'IA generi o modifiqui el contingut del butlletí..."
              }
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              rows={4}
              disabled={apiLoading}
            />
            <p className="text-xs text-muted-foreground">
              Si no especifiques instruccions, l'IA generarà contingut basat en el context actual.
            </p>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={apiLoading}
            >
              Cancel·lar
            </Button>
            <Button
              type="submit"
              disabled={apiLoading || !canEditNewsletters}
              className="bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600"
              title={!canEditNewsletters ? "You don't have permission to generate newsletter content" : undefined}
            >
              {apiLoading ? (
                <>
                  <Spinner className="mr-2 h-4 w-4" variant="default" />
                  Generant...
                </>
              ) : (
                <>
                  <Sparkles className="mr-2 h-4 w-4" />
                  Generar amb IA
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
