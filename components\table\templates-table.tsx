"use client"

import * as React from "react"
import { ColumnDef, ColumnFiltersState, SortingState, StringOrTemplateHeader, VisibilityState, flexRender, getCoreRowModel, getFilteredRowModel, getPaginationRowModel, getSortedRowModel, useReactTable } from "@tanstack/react-table"
import { ArrowUpDown, Eye, Pencil, Trash2, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog"
import { TemplateListItem } from "@/types/template"
import { useDeleteTemplate } from "@/hooks/use-delete-template"
import { ViewTemplateModal } from "@/components/modals/view-template-modal"
import { EditTemplateModal } from "@/components/modals/edit-template-modal"
import { Spinner } from "../ui/shadcn-io/spinner"

interface TemplatesTableProps {
  data: TemplateListItem[]
  loading: boolean
  onRefresh: () => void
  pagination?: {
    page: number
    pageSize: number
    totalCount: number
    hasNext: boolean
    hasPrevious: boolean
    onPageChange: (page: number) => void
    onPageSizeChange: (size: number) => void
  }
}

export function TemplatesTable({ data, loading, onRefresh, pagination }: TemplatesTableProps) {
  const [sorting, setSorting] = React.useState<SortingState>([])
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({})
  const [rowSelection, setRowSelection] = React.useState({})

  const [viewModalOpen, setViewModalOpen] = React.useState(false)
  const [editModalOpen, setEditModalOpen] = React.useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false)
  const [selectedTemplate, setSelectedTemplate] = React.useState<TemplateListItem | null>(null)

  const { deleteTemplate, loading: deleteLoading } = useDeleteTemplate()

  const handleView = (tpl: TemplateListItem) => {
    setSelectedTemplate(tpl)
    setViewModalOpen(true)
  }

  const handleEdit = (tpl: TemplateListItem) => {
    setSelectedTemplate(tpl)
    setEditModalOpen(true)
  }

  const handleDelete = (tpl: TemplateListItem) => {
    setSelectedTemplate(tpl)
    setDeleteDialogOpen(true)
  }

  const confirmDelete = async () => {
    if (!selectedTemplate) return
    try {
      await deleteTemplate(selectedTemplate.id)
      setDeleteDialogOpen(false)
      setSelectedTemplate(null)
      onRefresh()
    } catch (e) {
      console.error(e)
    }
  }

  const handleEditSuccess = () => {
    onRefresh()
  }

  const truncateText = (text: string, maxLength: number) => {
    if (!text) return ""
    return text.length > maxLength ? text.substring(0, maxLength) + "..." : text
  }

  const columns: ColumnDef<TemplateListItem>[] = [
    {
      accessorKey: "brand_name",
      header: "Marca",
      cell: ({ row }) => <div>{row.getValue("brand_name")}</div>,
    },
    {
      accessorKey: "name",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            Nom
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        )
      },
      cell: ({ row }) => {
        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div
                  className={"font-medium pl-4 cursor-pointer hover:text-primary hover:underline max-w-[200px] truncate"}
                  onClick={() => {
                    setSelectedTemplate(row.original)
                    setViewModalOpen(true)
                  }}
                >
                  {row.getValue("name")}
                </div>
              </TooltipTrigger>
              <TooltipContent>
                {row.getValue("name")}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )
      },
    },
    {
      accessorKey: "description",
      header: "Descripció",
      cell: ({ row }) => {
        const description = row.getValue("description") as string
        const truncatedDescription = truncateText(description, 150)
        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="max-w-[250px] cursor-default">
                  {truncatedDescription || "Sense descripció"}
                </div>
              </TooltipTrigger>
              {description && description.length > 150 && (
                <TooltipContent>
                  <p className="max-w-sm">{description}</p>
                </TooltipContent>
              )}
            </Tooltip>
          </TooltipProvider>
        )
      },
    },
    { accessorKey: "total_blocks", header: "Blocs" },
    {
      accessorKey: "created_at",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            className="m-0 p-0"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            Creat
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        )
      },
      cell: ({ row }) => {
        const date = new Date(row.getValue("created_at"))
        return <div>{date.toLocaleDateString()} {date.toLocaleTimeString()}</div>
      },
    },
    {
      accessorKey: "updated_at",
      header: "Actualitzat",
      cell: ({ row }) => {
        const date = new Date(row.getValue("updated_at"))
        return <div>{date.toLocaleDateString()} {date.toLocaleTimeString()}</div>
      },
    },
    {
      id: "actions",
      enableHiding: false,
      cell: ({ row }) => {
        const tpl = row.original

        return (
          <>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => handleEdit(tpl)}
            >
              <Pencil className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => handleDelete(tpl)}
            >
              <Trash2 className="h-4 w-4 text-red-600" />
            </Button>
          </>
        )
      },
    },
  ]

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    ...(pagination ? {} : { getPaginationRowModel: getPaginationRowModel() }),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    ...(pagination ? {
      manualPagination: true,
      pageCount: Math.ceil(pagination.totalCount / pagination.pageSize),
    } : {}),
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      ...(pagination ? {
        pagination: { pageIndex: pagination.page - 1, pageSize: pagination.pageSize },
      } : {}),
    },
  })

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-center h-64">
          <div className="flex flex-col items-center gap-2 mb-4">
            <div>
              <Spinner key="infinite" variant="infinite" size={64} />
            </div>
            <div className="text-center animate-pulse">
              Carregant plantilles...
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="w-full">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No s'han trobat plantilles.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination Controls */}
      {pagination && (
        <div className="flex items-center justify-between space-x-2 py-4">
          <div className="flex-1 text-sm text-muted-foreground">
            Mostrant {((pagination.page - 1) * pagination.pageSize) + 1} a{" "}
            {Math.min(pagination.page * pagination.pageSize, pagination.totalCount)} de{" "}
            {pagination.totalCount} entrades
          </div>
          <div className="flex items-center space-x-6 lg:space-x-8">
            <div className="flex items-center space-x-2">
              <p className="text-sm font-medium">Files per pàgina</p>
              <select
                value={pagination.pageSize}
                onChange={(e) => {
                  pagination.onPageSizeChange(Number(e.target.value))
                  pagination.onPageChange(1) // Reset to first page when changing page size
                }}
                className="h-8 w-[70px] rounded border border-input bg-background px-3 py-1 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
              >
                {[10, 20, 30, 40, 50].map((pageSize) => (
                  <option key={pageSize} value={pageSize}>
                    {pageSize}
                  </option>
                ))}
              </select>
            </div>
            <div className="flex w-[100px] items-center justify-center text-sm font-medium">
              Pàgina {pagination.page} de{" "}
              {Math.ceil(pagination.totalCount / pagination.pageSize)}
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                className="hidden h-8 w-8 p-0 lg:flex"
                onClick={() => pagination.onPageChange(1)}
                disabled={!pagination.hasPrevious}
              >
                <span className="sr-only">Anar a la primera pàgina</span>
                <ChevronsLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={() => pagination.onPageChange(pagination.page - 1)}
                disabled={!pagination.hasPrevious}
              >
                <span className="sr-only">Anar a la pàgina anterior</span>
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={() => pagination.onPageChange(pagination.page + 1)}
                disabled={!pagination.hasNext}
              >
                <span className="sr-only">Anar a la pàgina següent</span>
                <ChevronRight className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="hidden h-8 w-8 p-0 lg:flex"
                onClick={() => pagination.onPageChange(Math.ceil(pagination.totalCount / pagination.pageSize))}
                disabled={!pagination.hasNext}
              >
                <span className="sr-only">Anar a l'última pàgina</span>
                <ChevronsRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* View Modal */}
      <ViewTemplateModal templateId={selectedTemplate?.id || null} open={viewModalOpen} onOpenChange={setViewModalOpen} />

      {/* Edit Modal */}
      <EditTemplateModal templateId={selectedTemplate?.id || null} open={editModalOpen} onOpenChange={setEditModalOpen} onSuccess={handleEditSuccess} />

      {/* Delete Confirmation */}
      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Estàs segur?</AlertDialogTitle>
            <AlertDialogDescription>
              Aquesta acció no es pot desfer. Això eliminarà permanentment la plantilla
              "{selectedTemplate?.name}" i la traurà del sistema.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel·lar</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              disabled={deleteLoading}
              className="bg-red-600 hover:bg-red-700"
            >
              {deleteLoading ? 'Eliminant...' : 'Eliminar'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}

