"use client";

import Link from "next/link";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Users, Shield } from "lucide-react";

export default function ConfigurationPage() {
  const configurationSections = [
    {
      title: "Gestió d'usuaris",
      description: "",
      icon: Users,
      href: "/configuration/users",
      color: "text-primary"
    },
    {
      title: "Gestió de rols",
      description: "",
      icon: Shield,
      href: "/configuration/roles",
      color: "text-green-600"
    },
  ];

  return (
    <div className="p-6 space-y-6">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Usuaris i rols</h1>
        <p className="text-muted-foreground">
          Gestiona els usuaris i els seus permisos.
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-2">
        {configurationSections.map((section) => {
          const IconComponent = section.icon;
          return (
            <Link key={section.title} href={section.href}>
              <Card className="h-full transition-colors hover:bg-muted/50">
                <CardHeader className="flex flex-row items-center space-y-0 pb-2">
                  <div className="flex items-center space-x-2">
                    <IconComponent className={`h-6 w-6 ${section.color}`} />
                    <CardTitle className="text-lg">{section.title}</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-sm">
                    {section.description}
                  </CardDescription>
                </CardContent>
              </Card>
            </Link>
          );
        })}
      </div>
    </div>
  );
}
