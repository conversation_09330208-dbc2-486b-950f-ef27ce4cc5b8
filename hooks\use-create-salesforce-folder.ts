import { useState, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { CreateSalesforceFolderRequest, CreateSalesforceFolderResponse, SalesforceFolder } from '@/types/salesforce'
import { extractApiErrorMessage } from '@/lib/api-error-utils'
import { DEFAULT_API_URL } from '@/constants/constants'

interface UseCreateSalesforceFolderReturn {
  createFolder: (data: CreateSalesforceFolderRequest) => Promise<SalesforceFolder>
  loading: boolean
  error: string | null
}

export function useCreateSalesforceFolder(): UseCreateSalesforceFolderReturn {
  const { data: session } = useSession()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const createFolder = useCallback(async (data: CreateSalesforceFolderRequest): Promise<SalesforceFolder> => {
    if (!session?.djangoAccessToken) {
      throw new Error('Autenticaci<PERSON> requerida')
    }

    setLoading(true)
    setError(null)

    try {
      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || DEFAULT_API_URL
      
      const response = await fetch(`${backendUrl}/salesforce/folders/create/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.djangoAccessToken}`
        },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        const errorMessage = await extractApiErrorMessage(response, 'Failed to create Salesforce folder')
        throw new Error(errorMessage)
      }

      const result: CreateSalesforceFolderResponse = await response.json()
      
      if (result.success && result.folder) {
        return result.folder
      } else {
        throw new Error('Invalid response format from create folder API')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while creating the folder'
      setError(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }, [session?.djangoAccessToken])

  return {
    createFolder,
    loading,
    error
  }
}
