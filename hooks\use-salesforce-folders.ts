import { useState, useEffect, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { SalesforceFoldersResponse, SalesforceFolder, FlatSalesforceFolder } from '@/types/salesforce'
import { DEFAULT_API_URL } from '@/constants/constants'
import { extractApiErrorMessage } from '@/lib/api-error-utils'

interface UseSalesforceFoldersReturn {
  folders: SalesforceFolder[]
  flatFolders: FlatSalesforceFolder[]
  loading: boolean
  error: string | null
  refetch: () => void
}

export function useSalesforcefolders(): UseSalesforceFoldersReturn {
  const { data: session } = useSession()
  const [folders, setFolders] = useState<SalesforceFolder[]>([])
  const [flatFolders, setFlatFolders] = useState<FlatSalesforceFolder[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const getAuthHeaders = useCallback(() => {
    return {
      'Content-Type': 'application/json',
      ...(session?.djangoAccessToken && { 'Authorization': `Bearer ${session.djangoAccessToken}` })
    }
  }, [session?.djangoAccessToken])

  // Function to flatten folder tree for easier searching and selection
  const flattenFolders = useCallback((folders: SalesforceFolder[]): FlatSalesforceFolder[] => {
    const flattened: FlatSalesforceFolder[] = []
    
    const traverse = (folder: SalesforceFolder) => {
      flattened.push({
        id: folder.id,
        salesforce_id: folder.salesforce_id,
        name: folder.name,
        description: folder.description,
        level: folder.level,
        full_path: folder.full_path,
        is_root: folder.is_root,
        has_children: folder.has_children
      })
      
      if (folder.children) {
        folder.children.forEach(traverse)
      }
    }
    
    folders.forEach(traverse)
    return flattened
  }, [])

  const fetchFolders = useCallback(async () => {
    if (!session?.djangoAccessToken) {
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || DEFAULT_API_URL
      
      const response = await fetch(`${backendUrl}/salesforce/folders/`, {
        method: 'GET',
        headers: getAuthHeaders(),
      })

      if (!response.ok) {
        const errorMessage = await extractApiErrorMessage(response, 'Failed to fetch Salesforce folders')
        throw new Error(errorMessage)
      }

      const data: SalesforceFoldersResponse = await response.json()
      
      if (data.success && data.folders) {
        setFolders(data.folders)
        setFlatFolders(flattenFolders(data.folders))
      } else {
        throw new Error('Invalid response format from Salesforce folders API')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while fetching folders')
      setFolders([])
      setFlatFolders([])
    } finally {
      setLoading(false)
    }
  }, [session?.djangoAccessToken, getAuthHeaders, flattenFolders])

  const refetch = useCallback(async () => {
    if (!session?.djangoAccessToken) {
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || DEFAULT_API_URL
      
      const response = await fetch(`${backendUrl}/salesforce/sync-last-50-folders/`, {
        method: 'GET',
        headers: getAuthHeaders(),
      })

      if (!response.ok) {
        const errorMessage = await extractApiErrorMessage(response, 'Failed to fetch Salesforce folders')
        throw new Error(errorMessage)
      }

      const data: SalesforceFoldersResponse = await response.json()
      
      if (data.success && data.folders) {
        setFolders(data.folders)
        setFlatFolders(flattenFolders(data.folders))
      } else {
        throw new Error('Invalid response format from Salesforce folders API')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while fetching folders')
      setFolders([])
      setFlatFolders([])
    } finally {
      setLoading(false)
    }
  }, [session?.djangoAccessToken, getAuthHeaders, flattenFolders])

  useEffect(() => {
    fetchFolders()
  }, [fetchFolders])

  return {
    folders,
    flatFolders,
    loading,
    error,
    refetch
  }
}
