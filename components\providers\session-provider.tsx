"use client"

import { <PERSON>Provider as NextAuthSessionProvider } from "next-auth/react"
import { ReactNode } from "react"
import { ThemeProvider } from "../theme-provider"

interface ProvidersProps {
  children: ReactNode
}

export function Providers({ children }: ProvidersProps) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      disableTransitionOnChange={false}
    >
      <NextAuthSessionProvider>
        {children}
      </NextAuthSessionProvider>
    </ThemeProvider>
  )
}
