######################################################
# CONFIGURACIÓN HTTP - NEWSLETTER GRANDVALIRA RESORTS
######################################################

# Bloque HTTP para newsletter.grandvaliraresorts.com (PRODUCCIÓN)
server {
    listen 80;
    server_name newsletter.grandvaliraresorts.com;

    # API de Django (Gunicorn)
    location ^~ /apiv1/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_connect_timeout 120s;
        proxy_read_timeout 120s;
        proxy_send_timeout 120s;
    }

    # Archivos estáticos de Django
    location /static/ {
        alias /var/www/grandvalira-newsletters-back/staticfiles/;
        autoindex on;
    }

    # Archivos de medios
    location /media/ {
        alias /var/www/grandvalira-newsletters-back/media/;
        autoindex on;
    }

    # App Next.js en puerto 3000
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Página de error personalizada 502
    error_page 502 = /mantenimiento.html;
    location = /mantenimiento.html {
        root /var/www/html;
        internal;
    }

    # Gzip
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    gzip_min_length 256;
    gzip_proxied any;
    gzip_vary on;
}

# Bloque HTTP para pre.newsletter.grandvaliraresorts.com (PRE-PRODUCCIÓN)
server {
    listen 80;
    server_name pre.newsletter.grandvaliraresorts.com;

    # API de Django
    location ^~ /apiv1/ {
        proxy_pass http://127.0.0.1:8001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_connect_timeout 120s;
        proxy_read_timeout 120s;
        proxy_send_timeout 120s;
    }

    # Archivos estáticos de Django
    location /static/ {
        alias /var/www/grandvalira-newsletters-back-pre/staticfiles/;
        autoindex on;
    }

    # Archivos de medios
    location /media/ {
        alias /var/www/grandvalira-newsletters-back-pre/media/;
        autoindex on;
    }

    # App Next.js en puerto 3000 (updated from second file)
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Página de error personalizada 502
    error_page 502 = /mantenimiento.html;
    location = /mantenimiento.html {
        root /var/www/html;
        internal;
    }

    # Gzip
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    gzip_min_length 256;
    gzip_proxied any;
    gzip_vary on;
}

######################################################
# CONFIGURACIONES COMENTADAS PARA REFERENCIA FUTURA
######################################################

# NOTA: Las siguientes configuraciones están comentadas
# para referencia futura con los dominios anteriores

######################################################
# BLOQUE HTTPS: newsletter.grandvalira.com (COMENTADO)
######################################################
# server {
#     listen 443 ssl;
#     server_name newsletter.grandvalira.com;
#
#     # Certificados SSL
#     ssl_certificate /etc/letsencrypt/live/newsletter.grandvalira.com/fullchain.pem; # managed by Certbot
#     ssl_certificate_key /etc/letsencrypt/live/newsletter.grandvalira.com/privkey.pem; # managed by Certbot
#     include /etc/letsencrypt/options-ssl-nginx.conf;
#     ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;
#
#     # API de Django (Gunicorn)
#     location ^~ /apiv1/  {
#         proxy_pass http://127.0.0.1:8000;
#         proxy_set_header Host $host;
#         proxy_set_header X-Real-IP $remote_addr;
#         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#         proxy_set_header X-Forwarded-Proto $scheme;
#
#         proxy_connect_timeout 60s;
#         proxy_read_timeout 60s;
#         proxy_send_timeout 60s;
#     }
#
#     # Archivos estáticos de Django
#     location /static/ {
#         alias /var/www/shonmott-api/staticfiles/;
#         autoindex on;
#     }
#
#     # Archivos de medios
#     location /media/ {
#         alias /var/www/shonmott-api/media/;
#         autoindex on;
#     }
#
#     # App Next.js en puerto 3000
#     location / {
#         proxy_pass http://localhost:3000;
#         proxy_http_version 1.1;
#         proxy_set_header Upgrade $http_upgrade;
#         proxy_set_header Connection 'upgrade';
#         proxy_set_header Host $host;
#         proxy_set_header X-Real-IP $remote_addr;
#         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#         proxy_set_header X-Forwarded-Proto $scheme;
#         proxy_cache_bypass $http_upgrade;
#     }
#
#     # Página de error personalizada 502
#     error_page 502 = /mantenimiento.html;
#     location = /mantenimiento.html {
#         root /var/www/html;
#     }
#
#     # Gzip
#     gzip on;
#     gzip_min_length 256;
#     gzip_proxied any;
# }

######################################################
# REDIRECCIONES ANTIGUAS (COMENTADAS)
######################################################

# server {
#     if ($host = newsletter.grandvalira.com) {
#         return 301 https://$host$request_uri;
#     } # managed by Certbot
#
#     listen 80;
#     server_name newsletter.grandvalira.com;
#     return 404; # managed by Certbot
# }

# server {
#     if ($host = pre.newsletter.grandvalira.com) {
#         return 301 https://$host$request_uri;
#     } # managed by Certbot
#
#     listen 80;
#     server_name pre.newsletter.grandvalira.com;
#     return 404; # managed by Certbot
# }