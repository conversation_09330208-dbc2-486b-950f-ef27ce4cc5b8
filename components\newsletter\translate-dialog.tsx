"use client"

import * as React from "react"
import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Languages, Globe, Send } from "lucide-react"
import { Spinner } from "../ui/shadcn-io/spinner"
import { useLanguageContext } from "@/contexts/language-context"
import { useTranslateApi } from "@/hooks/use-translate-api"

interface TranslateDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  newsletterId: string
  onTranslateResponse: (response: any, targetLanguage: string) => void
}

export function TranslateDialog({
  open,
  onOpenChange,
  newsletterId,
  onTranslateResponse
}: TranslateDialogProps) {
  const { selectedLanguage, languages } = useLanguageContext()
  const { translateNewsletter, loading: apiLoading, error: apiError } = useTranslateApi()
  const [context, setContext] = useState("")
  const [baseLanguage, setBaseLanguage] = useState("")

  // Set default base language when dialog opens
  React.useEffect(() => {
    if (open && !baseLanguage) {
      setBaseLanguage(selectedLanguage)
    }
  }, [open, selectedLanguage, baseLanguage])

  const getLanguageDisplay = (langCode: string) => {
    const languageMap: Record<string, { display: string; color: string }> = {
      'es': { display: 'Espanyol', color: 'text-red-500' },
      'ca': { display: 'Català', color: 'text-yellow-500' },
      'fr': { display: 'Francès', color: 'text-blue-500' },
      'en': { display: 'Anglès', color: 'text-green-500' }
    }
    return languageMap[langCode] || { display: langCode.toUpperCase(), color: 'text-gray-500' }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (apiLoading || !baseLanguage) return

    try {
      const response = await translateNewsletter({
        newsletter_id: newsletterId,
        source_language: baseLanguage,
        user_input: context.trim() || "Translate this newsletter content while maintaining the original structure and formatting."
      })

      onTranslateResponse(response, baseLanguage)
      onOpenChange(false)
      setContext("")
    } catch (error) {
      console.error('Error calling translation API:', error)
      // Error is already handled by the useTranslateApi hook
    }
  }

  const handleCancel = () => {
    onOpenChange(false)
    setContext("")
  }

  // Get available base languages from context
  const availableBaseLanguages = languages.map(lang => lang.language)

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Languages className="h-5 w-5 text-blue-600" />
            Traduir Newsletter
          </DialogTitle>
          <DialogDescription>
            Tradueix el contingut de la newsletter utilitzant intel·ligència artificial.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Base Language Selector */}
          <div className="space-y-2">
            <Label htmlFor="base-language">Idioma base</Label>
            <Select value={baseLanguage} onValueChange={setBaseLanguage}>
              <SelectTrigger>
                <SelectValue placeholder="Selecciona l'idioma base" />
              </SelectTrigger>
              <SelectContent>
                {availableBaseLanguages.map((langCode) => {
                  const { display, color } = getLanguageDisplay(langCode)
                  return (
                    <SelectItem key={langCode} value={langCode}>
                      <div className="flex items-center gap-2">
                        <Globe className={`h-3 w-3 ${color}`} />
                        <span>{display}</span>
                      </div>
                    </SelectItem>
                  )
                })}
              </SelectContent>
            </Select>
          </div>

          {/* Context Input */}
          <div className="space-y-2">
            <Label htmlFor="translate-context">Context per a la traducció</Label>
            <Textarea
              id="translate-context"
              placeholder="Proporciona context sobre el contingut, audiència objectiu, to desitjat, o qualsevol informació rellevant per a la traducció..."
              value={context}
              onChange={(e) => setContext(e.target.value)}
              className="min-h-[100px] resize-none"
              disabled={apiLoading}
            />
            <p className="text-xs text-muted-foreground">
              El context ajuda a generar una traducció més precisa i adequada per al teu públic objectiu.
            </p>
          </div>

          {/* Error Display */}
          {apiError && (
            <div className="text-sm text-red-600 bg-red-50 p-3 rounded-md">
              {apiError}
            </div>
          )}

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={apiLoading}
            >
              Cancel·lar
            </Button>
            <Button
              type="submit"
              disabled={apiLoading || !baseLanguage}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {apiLoading ? (
                <>
                  <Spinner className="mr-2 h-4 w-4" variant="default" />
                  Traduint...
                </>
              ) : (
                <>
                  <Send className="mr-2 h-4 w-4" />
                  Traduir
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
