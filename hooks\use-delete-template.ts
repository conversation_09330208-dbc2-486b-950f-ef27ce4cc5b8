import { useState } from 'react'
import { useSession } from 'next-auth/react'
import { toast } from 'sonner'
import { DEFAULT_API_URL } from '@/constants/constants'
import { extractApiErrorMessage } from '@/lib/api-error-utils'

export function useDeleteTemplate() {
  const { data: session } = useSession()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const deleteTemplate = async (id: string) => {
    if (!session?.djangoAccessToken) {
      throw new Error('Autenticació requerida')
    }

    setLoading(true)
    setError(null)

    try {
      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || DEFAULT_API_URL

      const response = await fetch(`${backendUrl}/templates/${id}/delete/`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.djangoAccessToken}`
        }
      })

      if (!response.ok) {
        const errorMessage = await extractApiErrorMessage(response, 'No s\'ha pogut eliminar la plantilla')
        throw new Error(errorMessage)
      }

      toast.success('Plantilla eliminada correctament!')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'No s\'ha pogut eliminar la plantilla'
      setError(errorMessage)
      toast.error(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }

  return { deleteTemplate, loading, error }
}

