/**
 * Utility functions for detecting changes in form data
 */

/**
 * Deep comparison function to check if two objects are equal
 */
export function deepEqual(obj1: any, obj2: any): boolean {
  if (obj1 === obj2) {
    return true
  }

  if (obj1 == null || obj2 == null) {
    return obj1 === obj2
  }

  if (typeof obj1 !== typeof obj2) {
    return false
  }

  if (typeof obj1 !== 'object') {
    return obj1 === obj2
  }

  if (Array.isArray(obj1) !== Array.isArray(obj2)) {
    return false
  }

  if (Array.isArray(obj1)) {
    if (obj1.length !== obj2.length) {
      return false
    }
    for (let i = 0; i < obj1.length; i++) {
      if (!deepEqual(obj1[i], obj2[i])) {
        return false
      }
    }
    return true
  }

  const keys1 = Object.keys(obj1)
  const keys2 = Object.keys(obj2)

  if (keys1.length !== keys2.length) {
    return false
  }

  for (const key of keys1) {
    if (!keys2.includes(key)) {
      return false
    }
    if (!deepEqual(obj1[key], obj2[key])) {
      return false
    }
  }

  return true
}

/**
 * Check if form data has changed compared to original data
 */
export function hasChanges(originalData: any, currentData: any): boolean {
  return !deepEqual(originalData, currentData)
}

/**
 * Extract relevant fields for comparison from block data
 */
export function extractBlockData(block: any) {
  return {
    name: block.name || '',
    brand: block.brand || '',
    html_content: block.html_content || '',
    description: block.description || '',
    variables: block.variables || [],
    is_active: block.is_active ?? true
  }
}

/**
 * Extract relevant fields for comparison from header/footer data
 */
export function extractHeaderFooterData(headerFooter: any) {
  return {
    name: headerFooter.name || '',
    brand: headerFooter.brand || '',
    element_type: headerFooter.element_type || 'header',
    html_content: headerFooter.html_content || '',
    variables: headerFooter.variables || [],
    is_active: headerFooter.is_active ?? true
  }
}

/**
 * Extract relevant fields for comparison from user data
 */
export function extractUserData(user: any) {
  return {
    name: user.name || '',
    email: user.email || '',
    role: user.role || '',
    status: user.status || 'active'
  }
}

/**
 * Extract relevant fields for comparison from role data
 */
export function extractRoleData(role: any) {
  return {
    name: role.name || '',
    permissions: role.permissions || []
  }
}