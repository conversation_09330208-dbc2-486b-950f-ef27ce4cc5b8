"use client";

import { RoleManagement } from "@/components/roles/role-management";
import React, { useState, useEffect } from "react";
import { toast } from "sonner";
import { useRoles } from "@/hooks/use-roles";
import { usePermissions } from "@/hooks/use-permissions";
import { Spinner } from "@/components/ui/shadcn-io/spinner";

export default function Page() {
  // Pagination state
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [searchQuery, setSearchQuery] = useState("")
  const [debouncedSearch, setDebouncedSearch] = useState("")

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(searchQuery)
    }, 300)

    return () => clearTimeout(timer)
  }, [searchQuery])

  // Reset page when search changes
  useEffect(() => {
    setPage(1)
  }, [debouncedSearch])

  const {
    roles,
    loading: rolesLoading,
    error: rolesError,
    totalCount,
    hasNext,
    hasPrevious,
    refetch,
    createRole,
    updateRole,
    deleteRole
  } = useRoles({
    search: debouncedSearch || undefined,
    page,
    pageSize
  });

  const {
    permissions,
    loading: permissionsLoading,
    error: permissionsError
  } = usePermissions();

  const loading = rolesLoading || permissionsLoading;
  const error = rolesError || permissionsError;

  const handleCreateRole = async (roleData: { name: string; permissions: number[] }) => {
    try {
      await createRole(roleData);
      toast.success("Rol creat correctament.");
      refetch() // Refresh the current page
    } catch (error) {
      console.error('Error creating role:', error);
      toast.error("Error en crear el rol.");
    }
  };

  const handleUpdateRole = async (id: number, roleData: { name: string; permissions: number[] }) => {
    try {
      await updateRole(id, roleData);
      toast.success("Rol actualitzat correctament.");
      refetch() // Refresh the current page
    } catch (error) {
      console.error('Error updating role:', error);
      toast.error("Error en actualitzar el rol.");
    }
  };

  const handleDeleteRole = async (id: number) => {
    try {
      await deleteRole(id);
      toast.success("Rol eliminat correctament.");
      refetch() // Refresh the current page
    } catch (error) {
      console.error('Error deleting role:', error);
      toast.error("Error en eliminar el rol.");
    }
  };

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-center h-64">
          <div className="flex flex-col items-center gap-2 mb-4">
            <div>
              <Spinner key="infinite" variant="infinite" size={64} />
            </div>
            <div className="text-center animate-pulse">
              Carregant...
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-lg text-red-500">Error: {error}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="">
        <RoleManagement
          roles={roles}
          permissions={permissions}
          onCreateRole={handleCreateRole}
          onUpdateRole={handleUpdateRole}
          onDeleteRole={handleDeleteRole}
          searchQuery={searchQuery}
          onSearchChange={setSearchQuery}
          pagination={{
            page,
            pageSize,
            totalCount,
            hasNext,
            hasPrevious,
            onPageChange: setPage,
            onPageSizeChange: setPageSize
          }}
        />
      </div>
    </div>
  );
}