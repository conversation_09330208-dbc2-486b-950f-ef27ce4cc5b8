import { useState, useEffect, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { TemplateListItem, TemplateResponse } from '@/types/template'
import { DEFAULT_API_URL } from '@/constants/constants'
import { useAuthFetch } from './use-auth-fetch'
import { extractApiErrorMessage } from '@/lib/api-error-utils'

interface UseTemplatesParams {
  search?: string
  brand?: string
  page?: number
  pageSize?: number
}

interface UseTemplatesReturn {
  templates: TemplateListItem[]
  loading: boolean
  error: string | null
  totalCount: number
  hasNext: boolean
  hasPrevious: boolean
  refetch: () => void
}

export function useTemplates(params: UseTemplatesParams = {}): UseTemplatesReturn {
  const { data: session } = useSession()
  const { authFetch } = useAuthFetch()
  const [templates, setTemplates] = useState<TemplateListItem[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [totalCount, setTotalCount] = useState(0)
  const [hasNext, setHasNext] = useState(false)
  const [hasPrevious, setHasPrevious] = useState(false)

  const fetchTemplates = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      if (!session?.djangoAccessToken) {
        setError('Usuari no autenticat')
        setTemplates([])
        setTotalCount(0)
        setHasNext(false)
        setHasPrevious(false)
        return
      }

      const queryParams = new URLSearchParams()
      if (params.search) queryParams.append('search', params.search)
      if (params.brand) queryParams.append('brand', params.brand)
      if (params.page) queryParams.append('page', params.page.toString())
      if (params.pageSize) queryParams.append('page_size', params.pageSize.toString())

      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || DEFAULT_API_URL

      const response = await authFetch(`${backendUrl}/templates/list-templates/?${queryParams.toString()}`, {
        method: 'GET',
      })

      if (!response.ok) {
        const errorMessage = await extractApiErrorMessage(response, 'No s\'han pogut carregar les plantilles')
        throw new Error(errorMessage)
      }

      const data: TemplateResponse = await response.json()

      setTemplates(data.results)
      setTotalCount(data.count)
      setHasNext(!!data.next)
      setHasPrevious(!!data.previous)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'S\'ha produ\u00eft un error')
      setTemplates([])
      setTotalCount(0)
      setHasNext(false)
      setHasPrevious(false)
    } finally {
      setLoading(false)
    }
  }, [params.search, params.brand, params.page, params.pageSize, session?.djangoAccessToken, authFetch])

  useEffect(() => {
    fetchTemplates()
  }, [fetchTemplates])

  return {
    templates,
    loading,
    error,
    totalCount,
    hasNext,
    hasPrevious,
    refetch: fetchTemplates
  }
}

