"use client"

import React, { useState } from 'react'
import { SalesforceFolder } from '@/types/salesforce'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { ChevronDown, ChevronRight, Folder, FolderOpen, Check } from 'lucide-react'
import { cn } from '@/lib/utils'

interface FolderTreeProps {
  folders: SalesforceFolder[]
  selectedFolderId?: string
  onFolderSelect: (folder: SalesforceFolder) => void
  onCreateFolder?: (parentFolder: SalesforceFolder) => void
  className?: string
}

interface FolderNodeProps {
  folder: SalesforceFolder
  selectedFolderId?: string
  onFolderSelect: (folder: SalesforceFolder) => void
  onCreateFolder?: (parentFolder: SalesforceFolder) => void
  level?: number
}

const FolderNode: React.FC<FolderNodeProps> = ({
  folder,
  selectedFolderId,
  onFolderSelect,
  onCreateFolder,
  level = 0
}) => {
  // Check if folder has children based on the actual children array, not the has_children flag
  const hasChildren = folder.children && folder.children.length > 0
  const isSelected = selectedFolderId === folder.id

  // Function to check if this folder contains the selected folder in its children tree
  const containsSelectedFolder = React.useCallback((folder: SalesforceFolder, selectedId?: string): boolean => {
    if (!selectedId || !folder.children) return false
    
    return folder.children.some(child => 
      child.id === selectedId || containsSelectedFolder(child, selectedId)
    )
  }, [])

  // Auto-expand if this folder contains the selected folder
  const shouldAutoExpand = containsSelectedFolder(folder, selectedFolderId)
  const [isOpen, setIsOpen] = useState(shouldAutoExpand)

  // Update isOpen when selectedFolderId changes
  React.useEffect(() => {
    if (shouldAutoExpand && !isOpen) {
      setIsOpen(true)
    }
  }, [shouldAutoExpand, isOpen])

  const handleToggle = () => {
    if (hasChildren) {
      setIsOpen(!isOpen)
    }
  }

  const handleCheckboxChange = (checked: boolean) => {
    if (checked) {
      // Select this folder (radio button behavior - only one can be selected)
      onFolderSelect(folder)
    }
    // Note: We don't handle unchecking because we want radio button behavior
    // where once a folder is selected, clicking on it again doesn't deselect it
    // Users can only change selection by selecting a different folder
  }

  const handleCreateFolder = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (onCreateFolder) {
      onCreateFolder(folder)
    }
  }

  return (
    <div className="w-full">
      <div
        className={cn(
          "flex items-center gap-3 p-3 rounded-md transition-colors",
          isSelected
            ? "bg-blue-100 border border-blue-300 text-blue-900"
            : "hover:bg-muted/50",
          "group"
        )}
        style={{ paddingLeft: `${level * 16 + 8}px` }}
      >
        {/* Expand/Collapse Button */}
        <Button
          variant="ghost"
          size="sm"
          className="h-6 w-6 p-0"
          onClick={handleToggle}
          disabled={!hasChildren}
        >
          {hasChildren ? (
            isOpen ? (
              <ChevronDown className="h-4 w-4" />
            ) : (
              <ChevronRight className="h-4 w-4" />
            )
          ) : (
            <div className="h-4 w-4" />
          )}
        </Button>

        {/* Checkbox for selection */}
        <Checkbox
          checked={isSelected}
          onCheckedChange={handleCheckboxChange}
          className="flex-shrink-0"
          aria-label={`Select folder ${folder.name}`}
        />

        {/* Folder Icon */}
        <div className="flex-shrink-0">
          {isOpen ? (
            <FolderOpen className={cn("h-4 w-4", isSelected ? "text-blue-700" : "text-blue-600")} />
          ) : (
            <Folder className={cn("h-4 w-4", isSelected ? "text-blue-700" : "text-blue-600")} />
          )}
        </div>

        {/* Folder Name and Description */}
        <div className="flex-1 min-w-0">
          <p className={cn("text-sm font-medium truncate", isSelected && "text-blue-900")}>
            {folder.name}
          </p>
          {folder.description && (
            <p className={cn("text-xs truncate", isSelected ? "text-blue-700" : "text-muted-foreground")}>
              {folder.description}
            </p>
          )}
        </div>

        {/* Create Folder Button */}
        {onCreateFolder && (
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
            onClick={handleCreateFolder}
            title="Crear subcarpeta"
          >
            <span className="text-xs">+</span>
          </Button>
        )}
      </div>

      {/* Children */}
      {hasChildren && (
        <Collapsible open={isOpen}>
          <CollapsibleContent className="space-y-1">
            {folder.children?.map((child) => (
              <FolderNode
                key={child.id}
                folder={child}
                selectedFolderId={selectedFolderId}
                onFolderSelect={onFolderSelect}
                onCreateFolder={onCreateFolder}
                level={level + 1}
              />
            ))}
          </CollapsibleContent>
        </Collapsible>
      )}
    </div>
  )
}

export const FolderTree: React.FC<FolderTreeProps> = ({
  folders,
  selectedFolderId,
  onFolderSelect,
  onCreateFolder,
  className
}) => {
  return (
    <div className={cn("space-y-1", className)}>
      {folders.map((folder) => (
        <FolderNode
          key={folder.id}
          folder={folder}
          selectedFolderId={selectedFolderId}
          onFolderSelect={onFolderSelect}
          onCreateFolder={onCreateFolder}
        />
      ))}
    </div>
  )
}
