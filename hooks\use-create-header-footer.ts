import { useState, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { HeaderFooter } from '@/types/block'
import { extractApiErrorMessage } from '@/lib/api-error-utils'
import { DEFAULT_API_URL } from '@/constants/constants'

interface Variable {
  name: string
  variable_type_id: string
  default_value: {
    es: string
    ca: string
    fr: string
    en: string
  }
}

export interface CreateHeaderFooterRequest {
  name: string
  brand: string
  element_type: 'header' | 'footer'
  html_content: string
  variables: Variable[]
  is_active: boolean
}

interface UseCreateHeaderFooterReturn {
  createHeaderFooter: (data: CreateHeaderFooterRequest) => Promise<HeaderFooter>
  loading: boolean
  error: string | null
}

export function useCreateHeaderFooter(): UseCreateHeaderFooterReturn {
  const { data: session } = useSession()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const createHeaderFooter = useCallback(async (data: C<PERSON><PERSON><PERSON><PERSON><PERSON>ooterRequest): Promise<HeaderFooter> => {
    if (!session?.djangoAccessToken) {
      throw new Error('Autenticació requerida')
    }

    try {
      setLoading(true)
      setError(null)

      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || DEFAULT_API_URL

      const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.djangoAccessToken}`
      }

      const response = await fetch(`${backendUrl}/blocks/create-header-footer/`, {
        method: 'POST',
        headers,
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        const errorMessage = await extractApiErrorMessage(response, 'No s\'ha pogut crear la capçalera/peu')
        throw new Error(errorMessage)
      }

      const headerFooter: HeaderFooter = await response.json()
      return headerFooter
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'S\'ha produït un error'
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setLoading(false)
    }
  }, [session?.djangoAccessToken])

  return {
    createHeaderFooter,
    loading,
    error
  }
}
