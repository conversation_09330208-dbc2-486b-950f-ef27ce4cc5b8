"use client"

import React, { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { useSession } from "next-auth/react"
import { ArrowLeft, Plus, Eye, Save, Variable, Globe, Tag, Copy, Check, SparklesIcon } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { HtmlEditor } from "@/components/ui/html-editor"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { toast } from "sonner"
import { useBrands } from "@/hooks/use-brands"
import { useCreateBlock } from "@/hooks/use-create-block"
import { useVariableTypes } from "@/hooks/use-variable-types"

interface Variable {
  name: string
  variable_type_id: string
  default_value: {
    es: string
    ca: string
    fr: string
    en: string
  }
}

interface BlockFormData {
  name: string
  brand: string
  html_content: string
  description: string
  variables: Variable[]
  is_active: boolean
}

export default function CreateBlockPage() {
  const router = useRouter()
  const { data: session, status } = useSession()
  const { brands, loading: brandsLoading } = useBrands()
  const { createBlock, loading: createLoading, error: createError } = useCreateBlock()
  const { variableTypes, loading: variableTypesLoading } = useVariableTypes()

  const [formData, setFormData] = useState<BlockFormData>({
    name: "",
    brand: "",
    html_content: "",
    description: "",
    variables: [],
    is_active: true
  })

  const [variables, setVariables] = useState<Variable[]>([])
  const [previewHtml, setPreviewHtml] = useState("")
  const [selectedLanguage, setSelectedLanguage] = useState("es")
  const [copied, setCopied] = useState(false)
  const [showConfirmDialog, setShowConfirmDialog] = useState(false)

  // Helper function to get variable type name
  const getVariableTypeName = (variableTypeId: string) => {
    const variableType = variableTypes.find(type => type.id === variableTypeId)
    return variableType ? variableType.display_name : 'Unknown Type'
  }

  const isVariableTypeAI = (variableTypeId: string) => {
    const variableType = variableTypes.find(type => type.id === variableTypeId)
    return variableType ? variableType.ai_generated : false
  }

  const handleBack = () => {
    router.push('/blocks')
  }

  // Update preview HTML when content, variables, or language change
  useEffect(() => {
    let html = formData.html_content

    // Replace variables in HTML with their values in the selected language
    formData.variables.forEach((variable) => {
      const regex = new RegExp(`\\{\\{\\s*${variable.name}\\s*\\}\\}`, 'g')
      const languageValue = variable.default_value[selectedLanguage as keyof typeof variable.default_value] || ''
      html = html.replace(regex, languageValue)
    })

    setPreviewHtml(html)
  }, [formData.html_content, formData.variables, selectedLanguage])

  // Copy HTML to clipboard
  const copyHtmlToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(previewHtml)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy HTML to clipboard:', err)
    }
  }

  // Extract variables from HTML content
  useEffect(() => {
    const variableRegex = /\{\{\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*\}\}/g
    const foundVariables = new Set<string>()
    let match

    while ((match = variableRegex.exec(formData.html_content)) !== null) {
      foundVariables.add(match[1])
    }

    // Update variables list
    const newVariables: Variable[] = Array.from(foundVariables).map(varName => {
      const existing = variables.find(v => v.name === varName)
      return existing || {
        name: varName,
        variable_type_id: variableTypes.length > 0 ? variableTypes[0].id : "",
        default_value: { es: '', ca: '', fr: '', en: '' }
      }
    })
    setVariables(newVariables)
    setFormData(prev => ({ ...prev, variables: newVariables }))
  }, [formData.html_content, variableTypes])

  const handleVariableChange = (varName: string, language: 'es' | 'ca' | 'fr' | 'en', value: string) => {
    const updatedVariables = variables.map(v =>
      v.name === varName
        ? { ...v, default_value: { ...v.default_value, [language]: value } }
        : v
    )

    setVariables(updatedVariables)
    setFormData(prev => ({ ...prev, variables: updatedVariables }))
  }

  const handleVariableTypeChange = (varName: string, variableTypeId: string) => {
    const updatedVariables = variables.map(v =>
      v.name === varName
        ? { ...v, variable_type_id: variableTypeId }
        : v
    )

    setVariables(updatedVariables)
    setFormData(prev => ({ ...prev, variables: updatedVariables }))
  }

  const loadExample = () => {
    const exampleHtml = `
    <div style="background-color: #f8f9fa; padding: 30px; max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        <h1 style="color: #5c6bc0; font-size: 28px; margin-bottom: 20px; text-align: center;">
            {{ titulo_hero_principal }}
        </h1>

        <p style="color: #555; line-height: 1.6; margin-bottom: 25px; font-size: 16px;">
            Check out our featured product of the month with special discounts!
        </p>

        <div style="text-align: center; margin-bottom: 25px; display: flex; justify-content: center; align-items: center;">
            <img src="https://media1.tenor.com/m/dsz4jV3F5vcAAAAd/sad-abitoads.gif" alt="Featured Product" style="max-width: 100%; height: auto; border-radius: 6px; border: 1px solid #e0e0e0; display: block; margin: 0 auto;" />
        </div>

        <div style="text-align: center; margin-bottom: 30px;">
            <a href="#product-link" style="background-color: #4caf50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold; display: inline-block; margin-right: 15px;">
                Shop Now!
            </a>

            <button style="background-color: #ff9800; color: white; padding: 12px 24px; border: none; border-radius: 4px; font-weight: bold; cursor: pointer;">
                Learn More
            </button>
        </div>

        <div style="border-top: 1px solid #e0e0e0; padding-top: 20px; font-size: 14px; color: #777; text-align: center;">
            © 2024 Your Company. All rights reserved.
        </div>
    </div>
    `

    setFormData(prev => ({
      ...prev,
      name: "Bloc d'Exemple",
      description: "Un bloc d'exemple amb variables",
      html_content: exampleHtml
    }))
  }

  const validateForm = () => {
    if (!formData.name.trim()) {
      toast.error('El nom del bloc és obligatori')
      return false
    }

    if (!formData.brand) {
      toast.error('Si us plau, selecciona una marca')
      return false
    }

    if (!formData.html_content.trim()) {
      toast.error('El contingut HTML és obligatori')
      return false
    }

    return true
  }

  const handleSubmit = () => {
    if (validateForm()) {
      setShowConfirmDialog(true)
    }
  }

  const confirmCreate = async () => {
    try {
      await createBlock(formData)
      toast.success('Bloc creat amb èxit!')
      setShowConfirmDialog(false)
      router.push('/blocks/block')
    } catch (error) {
      // Error is already handled in the hook and shown via toast
      console.error('Error creating block:', error)
      setShowConfirmDialog(false)
    }
  }


  // Show loading while session is being fetched
  if (status === "loading") {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-32">
          <div className="text-muted-foreground">Carregant...</div>
        </div>
      </div>
    )
  }

  // Redirect to login if not authenticated
  if (status === "unauthenticated") {
    router.push('/login')
    return null
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Crear Bloc</h1>
          <p className="text-muted-foreground">
            Crea un nou bloc de contingut per a les teves newsletters amb vista prèvia en directe
          </p>
        </div>
        <Button onClick={handleSubmit} disabled={createLoading}>
          <Save className="mr-2 h-4 w-4" />
          {createLoading ? 'Creant...' : 'Crear Bloc'}
        </Button>
      </div>

      {/* Error Display */}
      {createError && (
        <div className="p-4 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
          Error: {createError}
        </div>
      )}

      {/* Block Details - Single Row */}
      <Card>
        <CardHeader>
          <CardTitle>Detalls del Bloc</CardTitle>
          <CardDescription>
            Configura els paràmetres del teu bloc
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="brand">Marca</Label>
              <Select value={formData.brand} onValueChange={(value) => setFormData(prev => ({ ...prev, brand: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecciona una marca" />
                </SelectTrigger>
                <SelectContent>
                  {brands.map((brand) => (
                    <SelectItem key={brand.id} value={brand.id}>
                      {brand.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="name-block">Nom del Bloc</Label>
              <Input
                id="name"
                placeholder="Introdueix el nom del bloc"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value.substring(0, 70) }))}
                maxLength={70}
              />
              <p className="text-xs text-muted-foreground">{formData.name.length}/70 caràcters</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Descripció</Label>
              <Input
                id="description"
                placeholder="Introdueix la descripció del bloc"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value.substring(0, 150) }))}
                maxLength={150}
              />
              <p className="text-xs text-muted-foreground">{formData.description.length}/150 caràcters</p>
            </div>

            <div className="space-y-2">
              <Label>Configuració</Label>
              <div className="flex items-center gap-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={formData.is_active}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: checked }))}
                  />
                  <Label className="text-sm">Actiu</Label>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* HTML Editor and Preview - Side by Side */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Left Panel - HTML Editor */}
        <div className="space-y-6">
          {/* HTML Editor */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>
                    Contingut HTML
                  </CardTitle>
                  <CardDescription>
                    Escriu el teu contingut HTML. Utilitza {`{{ nomVariable }}`} per a les variables.
                  </CardDescription>
                </div>
                {/* <Button variant="outline" size="sm" onClick={loadExample}>
                  <Plus className="mr-2 h-4 w-4" />
                  Carregar Exemple
                </Button> */}
              </div>
            </CardHeader>
            <CardContent>
              <HtmlEditor
                placeholder={`Introdueix el teu contingut HTML aquí...`}
                value={formData.html_content}
                onChange={(value) => setFormData(prev => ({ ...prev, html_content: value }))}
                rows={20}
              />
            </CardContent>
          </Card>
        </div>

        {/* Right Panel - Preview */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between gap-2">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Eye className="h-5 w-5" />
                    Vista Prèvia en Directe
                  </CardTitle>
                  <CardDescription>
                    Veu com es veurà el teu bloc amb els valors de les variables
                  </CardDescription>
                </div>
                <div className="flex items-center gap-3">
                  <div className="flex items-center gap-2">
                    <Label className="text-sm font-medium">Idioma:</Label>
                    <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>
                      <SelectTrigger className="w-32">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="es">
                          <div className="flex items-center gap-2">
                            <Globe className="h-3 w-3 text-red-500" />
                            Espanyol
                          </div>
                        </SelectItem>
                        <SelectItem value="ca">
                          <div className="flex items-center gap-2">
                            <Globe className="h-3 w-3 text-yellow-600" />
                            Català
                          </div>
                        </SelectItem>
                        <SelectItem value="fr">
                          <div className="flex items-center gap-2">
                            <Globe className="h-3 w-3 text-blue-500" />
                            Francès
                          </div>
                        </SelectItem>
                        <SelectItem value="en">
                          <div className="flex items-center gap-2">
                            <Globe className="h-3 w-3 text-green-500" />
                            Anglès
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="border rounded-lg p-6 w-full shadow-inner">
                {previewHtml ? (
                  <div dangerouslySetInnerHTML={{ __html: previewHtml }} />
                ) : (
                  <div className="flex items-center justify-center h-full text-muted-foreground">
                    <div className="text-center">
                      <Eye className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>Comença a escriure HTML per veure la vista prèvia</p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Variables Section - Full Width */}
      {variables.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Variable className="h-5 w-5" />
              Variables del Bloc
            </CardTitle>
            <CardDescription>
              Configura els valors per defecte per a les variables trobades al teu HTML en tots els idiomes
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-8">
              {variables.map((variable, index) => (
                <div key={variable.name} className="relative">
                  {/* Variable Header */}
                  <div className="flex items-center justify-between mb-6 pb-4 border-b">
                    <div className="flex items-center gap-3">
                      <div className="flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-600 rounded-full text-sm font-medium">
                        {index + 1}
                      </div>
                      <div>
                        <h6 className="font-semibold text-gray-900 h-6">
                          {variable.name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </h6>
                        <p className="text-sm text-gray-500 font-mono">
                          {`{{ ${variable.name} }}`}
                        </p>
                        <div className="flex items-center gap-1 mt-1">
                          <Tag className="h-3 w-3 text-blue-500" />
                          <span className="text-xs text-blue-600 font-medium flex items-center gap-1">
                            {isVariableTypeAI(variable.variable_type_id) && (
                              <SparklesIcon className="h-3 w-3 text-yellow-500" />
                            )}
                            {getVariableTypeName(variable.variable_type_id)}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="flex items-center gap-2">
                        <Label className="text-sm text-gray-600">Tipus de Variable</Label>
                      </div>
                      <Select
                        value={variable.variable_type_id}
                        onValueChange={(value) => handleVariableTypeChange(variable.name, value)}
                        disabled={variableTypesLoading}
                      >
                        <SelectTrigger className="w-48">
                          <SelectValue placeholder={variableTypesLoading ? "Carregant..." : "Selecciona tipus"} />
                        </SelectTrigger>
                        <SelectContent>
                          {variableTypes.map((type) => (
                            <SelectItem key={type.id} value={type.id}>
                              <div className="flex flex-col">
                                <span className="font-medium text-left">{type.display_name}</span>
                                <span className="text-xs text-muted-foreground text-left flex items-center gap-1">
                                  {type.ai_generated && <SparklesIcon className="h-3 w-3 text-yellow-500" />}
                                  {type.field_type_display}
                                </span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* Language Inputs */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div className="space-y-3">
                      <div className="flex items-center gap-2">
                        <Globe className="h-4 w-4 text-red-500" />
                        <Label htmlFor={`var-${variable.name}-es`} className="font-medium">
                          Espanyol
                        </Label>
                        <span className="text-xs bg-red-100 text-red-700 px-2 py-1 rounded">ES</span>
                      </div>
                      <Input
                        id={`var-${variable.name}-es`}
                        placeholder="Introdueix el valor en espanyol"
                        value={variable.default_value.es}
                        onChange={(e) => handleVariableChange(variable.name, 'es', e.target.value)}
                        className="border-red-200 focus:border-red-400"
                      />
                    </div>

                    <div className="space-y-3">
                      <div className="flex items-center gap-2">
                        <Globe className="h-4 w-4 text-yellow-600" />
                        <Label htmlFor={`var-${variable.name}-ca`} className="font-medium">
                          Català
                        </Label>
                        <span className="text-xs bg-yellow-100 text-yellow-700 px-2 py-1 rounded">CA</span>
                      </div>
                      <Input
                        id={`var-${variable.name}-ca`}
                        placeholder="Introdueix el valor en català"
                        value={variable.default_value.ca}
                        onChange={(e) => handleVariableChange(variable.name, 'ca', e.target.value)}
                        className="border-yellow-200 focus:border-yellow-400"
                      />
                    </div>

                    <div className="space-y-3">
                      <div className="flex items-center gap-2">
                        <Globe className="h-4 w-4 text-blue-500" />
                        <Label htmlFor={`var-${variable.name}-fr`} className="font-medium">
                          Francès
                        </Label>
                        <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">FR</span>
                      </div>
                      <Input
                        id={`var-${variable.name}-fr`}
                        placeholder="Introdueix el valor en francès"
                        value={variable.default_value.fr}
                        onChange={(e) => handleVariableChange(variable.name, 'fr', e.target.value)}
                        className="border-blue-200 focus:border-blue-400"
                      />
                    </div>

                    <div className="space-y-3">
                      <div className="flex items-center gap-2">
                        <Globe className="h-4 w-4 text-green-500" />
                        <Label htmlFor={`var-${variable.name}-en`} className="font-medium">
                          Anglès
                        </Label>
                        <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">EN</span>
                      </div>
                      <Input
                        id={`var-${variable.name}-en`}
                        placeholder="Introdueix el valor en anglès"
                        value={variable.default_value.en}
                        onChange={(e) => handleVariableChange(variable.name, 'en', e.target.value)}
                        className="border-green-200 focus:border-green-400"
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Confirmation Dialog */}
      <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirmar Creació del Bloc</DialogTitle>
            <DialogDescription>
              Estàs segur que vols crear aquest bloc amb la següent configuració?
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">Nom:</span>
                <p className="text-muted-foreground">{formData.name}</p>
              </div>
              <div>
                <span className="font-medium">Marca:</span>
                <p className="text-muted-foreground">
                  {brands.find(b => b.id === formData.brand)?.name || formData.brand}
                </p>
              </div>
              <div>
                <span className="font-medium">Descripció:</span>
                <p className="text-muted-foreground">{formData.description || 'Sense descripció'}</p>
              </div>
              <div>
                <span className="font-medium">Variables:</span>
                <p className="text-muted-foreground">
                  {variables.length > 0 ? `${variables.length} variable(s)` : 'Cap variable'}
                </p>
                {variables.length > 0 && (
                  <div className="mt-2 text-xs text-muted-foreground">
                    {variables.map(v => v.name).join(', ')}
                  </div>
                )}
              </div>
            </div>

            <div className="flex gap-4 text-sm">
              <div className="flex items-center gap-2">
                <span className="font-medium">Actiu:</span>
                <span className={formData.is_active ? "text-green-600" : "text-red-600"}>
                  {formData.is_active ? 'Sí' : 'No'}
                </span>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowConfirmDialog(false)}
              disabled={createLoading}
            >
              Cancel·lar
            </Button>
            <Button
              onClick={confirmCreate}
              disabled={createLoading}
            >
              <Save className="mr-2 h-4 w-4" />
              {createLoading ? 'Creant...' : 'Crear Bloc'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
