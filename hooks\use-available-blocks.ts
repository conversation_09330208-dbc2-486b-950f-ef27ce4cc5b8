import { useState, useEffect, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { Block, BlockResponse } from '@/types/block'
import { DEFAULT_API_URL } from '@/constants/constants'
import { extractApiErrorMessage } from '@/lib/api-error-utils'

interface UseAvailableBlocksParams {
  brand?: string
  search?: string
  isActive?: boolean
}

interface UseAvailableBlocksReturn {
  blocks: Block[]
  loading: boolean
  error: string | null
  refetch: () => void
}

const API_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_URL || DEFAULT_API_URL

export function useAvailableBlocks(params: UseAvailableBlocksParams = {}): UseAvailableBlocksReturn {
  const { data: session } = useSession()
  const [blocks, setBlocks] = useState<Block[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchBlocks = useCallback(async () => {
    if (!session?.djangoAccessToken) {
      setError('Authentication required')
      return
    }

    try {
      setLoading(true)
      setError(null)

      const queryParams = new URLSearchParams()
      if (params.search) queryParams.append('search', params.search)
      if (params.brand) queryParams.append('brand', params.brand)
      if (params.isActive !== undefined) queryParams.append('is_active', params.isActive.toString())
      
      // Set default to only active blocks for newsletter building
      if (params.isActive === undefined) queryParams.append('is_active', 'true')

      const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.djangoAccessToken}`
      }

      const response = await fetch(`${API_BASE_URL}/blocks/list-blocks/?${queryParams.toString()}`, {
        method: 'GET',
        headers,
      })

      if (!response.ok) {
        const errorMessage = await extractApiErrorMessage(response, 'Failed to fetch available blocks')
        throw new Error(errorMessage)
      }

      const data: BlockResponse = await response.json()
      setBlocks(data.results)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
      setBlocks([])
    } finally {
      setLoading(false)
    }
  }, [params.search, params.brand, params.isActive, session])

  useEffect(() => {
    fetchBlocks()
  }, [fetchBlocks])

  return {
    blocks,
    loading,
    error,
    refetch: fetchBlocks
  }
}
