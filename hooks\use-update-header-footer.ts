import { useState, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { Header<PERSON>oot<PERSON> } from '@/types/block'
import { extractApiErrorMessage } from '@/lib/api-error-utils'
import { DEFAULT_API_URL } from '@/constants/constants'
import { toast } from 'sonner'

interface Variable {
  id?: string
  name: string
  variable_type_id: string
  default_value: {
    es: string
    ca: string
    fr: string
    en: string
  }
}

export interface UpdateHeaderFooterRequest {
  id: string
  name: string
  brand: string
  element_type: 'header' | 'footer'
  html_content: string
  variables: Variable[]
  is_active: boolean
}

interface UseUpdateHeaderFooterReturn {
  updateHeaderFooter: (data: UpdateHeaderFooterRequest) => Promise<HeaderFooter>
  loading: boolean
  error: string | null
}

export function useUpdateHeaderFooter(): UseUpdateHeaderFooterReturn {
  const { data: session } = useSession()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const updateHeaderFooter = useCallback(async (data: UpdateHeaderFooterRequest): Promise<HeaderFooter> => {
    if (!session?.djangoAccessToken) {
      throw new Error('Autenticació requerida')
    }

    try {
      setLoading(true)
      setError(null)

      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || DEFAULT_API_URL

      const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.djangoAccessToken}`
      }

      const response = await fetch(`${backendUrl}/blocks/update-header-footer/`, {
        method: 'PUT',
        headers,
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        const errorMessage = await extractApiErrorMessage(response, 'No s\'ha pogut actualitzar el header/footer')
        throw new Error(errorMessage)
      }

      const headerFooter: HeaderFooter = await response.json()
      toast.success('Header/Footer actualitzat correctament!')
      return headerFooter
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'S\'ha produït un error'
      setError(errorMessage)
      toast.error(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setLoading(false)
    }
  }, [session?.djangoAccessToken])

  return {
    updateHeaderFooter,
    loading,
    error
  }
}
