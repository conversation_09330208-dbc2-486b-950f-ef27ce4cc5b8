import type { NextConfig } from "next";

const removeImports = require("next-remove-imports")();


const path = require('path');

const nextConfig: NextConfig = {
  /* config options here */
  // Turbo configuration only for development
  ...(process.env.NODE_ENV === 'development' && {
    experimental: {
      turbo: {
        resolveAlias: {
          '@': '.',
        },
      },
    },
  }),
};

export default removeImports(nextConfig);
