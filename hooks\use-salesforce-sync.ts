import { useState, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { DEFAULT_API_URL } from '@/constants/constants'
import { extractApiErrorMessage } from '@/lib/api-error-utils'
import { SalesforceSyncRequest, SalesforceSyncResponse } from '@/types/salesforce'
import { toast } from 'sonner'

interface UseSalesforceSyncReturn {
  syncNewsletter: (newsletterParentId: string) => Promise<SalesforceSyncResponse>
  loading: boolean
  error: string | null
}

export function useSalesforceSync(): UseSalesforceSyncReturn {
  const { data: session } = useSession()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const getAuthHeaders = useCallback(() => {
    return {
      'Content-Type': 'application/json',
      ...(session?.djangoAccessToken && { 'Authorization': `Bearer ${session.djangoAccessToken}` })
    }
  }, [session?.djangoAccessToken])

  const syncNewsletter = useCallback(async (newsletterParentId: string): Promise<SalesforceSyncResponse> => {
    if (!session?.djangoAccessToken) {
      throw new Error('No authentication token available')
    }

    setLoading(true)
    setError(null)

    try {
      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || DEFAULT_API_URL

      const requestData: SalesforceSyncRequest = {
        newsletter_parent_id: newsletterParentId
      }

      const response = await fetch(`${backendUrl}/newsletters/synchronize/`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify(requestData)
      })

      if (!response.ok) {
        const errorMessage = await extractApiErrorMessage(response, 'Failed to synchronize newsletter with Salesforce')
        throw new Error(errorMessage)
      }

      const result: SalesforceSyncResponse = await response.json()

      // Show success/error toast based on results
      if (result.error_count === 0) {
        toast.success(`Sincronització completada: ${result.success_count} èxits`)
      } else if (result.success_count === 0) {
        toast.error(`Sincronització fallida: ${result.error_count} errors`)
      } else {
        toast.warning(`Sincronització parcial: ${result.success_count} èxits, ${result.error_count} errors`)
      }

      return result
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while synchronizing newsletter'
      setError(errorMessage)
      toast.error(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }, [session?.djangoAccessToken, getAuthHeaders])

  return {
    syncNewsletter,
    loading,
    error
  }
}